"""
云端API客户端
负责与Admin API进行通信，实现消息转发功能
"""

import asyncio
import aiohttp
import json
import time
import logging
from typing import Dict, Any, Optional

# 直接使用logging，避免循环导入
logger = logging.getLogger(__name__)


class CloudAPIClient:
    """云端API客户端"""
    
    def __init__(self, base_url: str, auth_token: str, timeout: int = 30, max_retries: int = 3):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.timeout = timeout
        self.max_retries = max_retries
        self.logger = logging.getLogger(__name__)
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
        
        self.logger.info(f"云端API客户端初始化完成: {self.base_url}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def start(self):
        """启动客户端"""
        if self.session is None:
            # 配置连接器
            connector = aiohttp.TCPConnector(
                limit=100,  # 连接池大小
                ttl_dns_cache=300,  # DNS缓存时间
                use_dns_cache=True,
            )
            
            # 配置超时
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            # 创建会话
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.auth_token}',
                    'User-Agent': 'QQBot-CloudClient/1.0'
                }
            )
            
            self.logger.info("云端API客户端会话已启动")
    
    async def close(self):
        """关闭客户端"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.info("云端API客户端会话已关闭")
    
    async def process_message(self, message_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理消息
        
        Args:
            message_data: 消息数据
            
        Returns:
            API响应数据，如果失败返回None
        """
        endpoint = "/message"
        return await self._request("POST", endpoint, data=message_data)
    
    async def get_status(self) -> Optional[Dict[str, Any]]:
        """
        获取服务状态
        
        Returns:
            状态数据，如果失败返回None
        """
        endpoint = "/status"
        return await self._request("GET", endpoint)
    
    async def get_config(self) -> Optional[Dict[str, Any]]:
        """
        获取配置信息
        
        Returns:
            配置数据，如果失败返回None
        """
        endpoint = "/config"
        return await self._request("GET", endpoint)
    
    async def _request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        发送HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            data: 请求数据
            
        Returns:
            响应数据，如果失败返回None
        """
        if not self.session:
            await self.start()
        
        url = f"{self.base_url}{endpoint}"
        
        for attempt in range(self.max_retries):
            try:
                start_time = time.time()
                
                # 发送请求
                async with self.session.request(method, url, json=data) as response:
                    response_time = (time.time() - start_time) * 1000
                    
                    # 读取响应
                    response_text = await response.text()
                    
                    # 记录详细的请求日志
                    self.logger.error(f"=== API请求详情 ===")
                    self.logger.error(f"URL: {url}")
                    self.logger.error(f"方法: {method}")
                    self.logger.error(f"状态码: {response.status}")
                    self.logger.error(f"响应时间: {response_time:.1f}ms")
                    if data:
                        self.logger.error(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
                    self.logger.error(f"=== 请求详情结束 ===")
                    
                    # 检查状态码
                    if response.status == 200:
                        try:
                            response_data = json.loads(response_text)
                            
                            # 检查业务状态码
                            if response_data.get('code') == 1:
                                return response_data.get('data', {})
                            else:
                                self.logger.warning(f"API业务错误: {response_data.get('msg', '未知错误')}")
                                return None
                                
                        except json.JSONDecodeError as e:
                            self.logger.error(f"API响应JSON解析失败: {e}")
                            return None
                    
                    elif response.status == 401:
                        self.logger.error("API认证失败，请检查Token配置")
                        return None
                    
                    elif response.status == 403:
                        self.logger.error("API访问被禁止，请检查IP白名单配置")
                        return None
                    
                    elif response.status >= 500:
                        self.logger.error(f"API服务器错误: {response.status}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(1 * (attempt + 1))  # 指数退避
                            continue
                        return None
                    
                    else:
                        self.logger.error(f"API请求失败: {response.status} - {response_text}")
                        return None
            
            except aiohttp.ClientError as e:
                self.logger.error(f"API请求网络错误 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))  # 指数退避
                    continue
                return None
            
            except asyncio.TimeoutError:
                self.logger.error(f"API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 * (attempt + 1))
                    continue
                return None
            
            except Exception as e:
                self.logger.error(f"API请求未知错误: {e}")
                return None
        
        return None
    
    def format_message_data(self, event, message_type: str) -> Dict[str, Any]:
        """
        格式化消息数据
        
        Args:
            event: 消息事件
            message_type: 消息类型
            
        Returns:
            格式化后的消息数据
        """
        # 提取纯文本消息
        message_text = event.message.extract_plain_text(include_at=True)
        
        # 构建基础数据
        data = {
            'message': message_text,
            'user_id': str(event.user_id),
            'message_type': message_type,
            'sender': {
                'nickname': event.sender.get('nickname', '未知用户'),
                'card': event.sender.get('card', ''),
                'role': event.sender.get('role', 'member')
            },
            'timestamp': time.time()
        }
        
        # 添加群组信息
        if message_type == 'group' and hasattr(event, 'group_id'):
            data['group_id'] = str(event.group_id)
        
        return data


class CloudAPIManager:
    """云端API管理器"""
    
    def __init__(self):
        self.client: Optional[CloudAPIClient] = None
        self.logger = logging.getLogger(__name__)
        self.command_prefixes = ['/', '！', '!']  # 默认命令前缀
    
    async def initialize(self, config: Dict[str, Any]) -> bool:
        """
        初始化API客户端
        
        Args:
            config: 配置信息
            
        Returns:
            是否初始化成功
        """
        try:
            if not config.get('enabled', False):
                self.logger.info("云端API功能已禁用")
                return False
            
            base_url = config.get('base_url', '').rstrip('/')
            auth_token = config.get('auth_token', '')
            timeout = config.get('timeout', 30)
            max_retries = config.get('max_retries', 3)
            
            # 更新命令前缀
            if 'command_prefixes' in config:
                self.command_prefixes = config['command_prefixes']
            
            if not base_url or not auth_token:
                self.logger.error("云端API配置不完整，请检查base_url和auth_token")
                return False
            
            # 创建客户端
            self.client = CloudAPIClient(base_url, auth_token, timeout, max_retries)
            await self.client.start()
            
            # 测试连接
            status = await self.client.get_status()
            if status is not None:
                self.logger.info("云端API连接测试成功")
                return True
            else:
                self.logger.error("云端API连接测试失败")
                return False
                
        except Exception as e:
            self.logger.error(f"初始化云端API客户端失败: {e}")
            return False
    
    async def close(self):
        """关闭API客户端"""
        if self.client:
            await self.client.close()
            self.client = None
    
    def is_available(self) -> bool:
        """检查API客户端是否可用"""
        return self.client is not None
    
    def is_command_message(self, message_text: str) -> bool:
        """
        检查消息是否为命令
        
        Args:
            message_text: 消息文本
            
        Returns:
            是否为命令消息
        """
        if not message_text:
            return False
        
        message_text = message_text.strip()
        return any(message_text.startswith(prefix) for prefix in self.command_prefixes)
    
    async def process_message(self, event, message_type: str) -> Optional[str]:
        """
        处理消息并返回回复内容
        
        Args:
            event: 消息事件
            message_type: 消息类型
            
        Returns:
            回复内容，如果没有回复返回None
        """
        if not self.is_available():
            return None
        
        try:
            # 提取消息文本
            message_text = event.message.extract_plain_text(include_at=True)
            
            # 检查是否为命令消息
            if not self.is_command_message(message_text):
                self.logger.info(f"非命令消息，跳过处理: {message_text[:20]}...")
                return None
            
            self.logger.info(f"检测到命令消息，转发到云端API: {message_text}")
            
            # 格式化消息数据
            message_data = self.client.format_message_data(event, message_type)
            
            # 发送到云端处理
            response = await self.client.process_message(message_data)
            
            if response and response.get('should_reply'):
                return response.get('reply')
            
            return None
            
        except Exception as e:
            self.logger.error(f"处理云端消息失败: {e}")
            return None


# 全局实例
cloud_api_manager = CloudAPIManager()
