<?php
// +----------------------------------------------------------------------
// | QQBot应用配置
// +----------------------------------------------------------------------

return [
    // 是否启用QQBot功能
    'enabled' => env('qqbot.enabled', true),
    
    // 认证Token列表 - 从.env读取，支持逗号分隔的多个Token
    'auth_tokens' => array_filter(explode(',', env('qqbot.auth_token', 'your-secure-token-here'))),
    
    // 消息配置
    'message_format' => [
        'max_length' => (int)env('qqbot.message_max_length', 4000),
        'enable_image' => env('qqbot.enable_image', false),
        'enable_at' => env('qqbot.enable_at', true),
    ],
    
    // 功能开关
    'features' => [
        'item_search' => true,
        'price_query' => true,
        'ranking' => true,
        'daily_password' => true,
        'special_operation' => true,
        'help' => true,
    ],
    
    // 缓存配置
    'cache' => [
        'enabled' => true,
        'prefix' => 'qqbot:',
        'ttl' => [
            'commands' => 300,      // 命令结果缓存5分钟
            'status' => 60,         // 状态信息缓存1分钟
            'config' => 3600,       // 配置信息缓存1小时
        ],
    ],
    
    // 安全配置
    'security' => [
        // IP白名单 - 从.env读取，支持逗号分隔的多个IP
        'ip_whitelist' => array_filter(explode(',', env('qqbot.ip_whitelist', ''))),
        // 用户黑名单 - 从.env读取，支持逗号分隔的多个用户ID
        'user_blacklist' => array_filter(explode(',', env('qqbot.user_blacklist', ''))),
        'group_whitelist' => [], // 群组白名单，空数组表示不限制
    ],
    
    // 业务配置
    'business' => [
        'default_page_size' => 10,
        'max_page_size' => 20,
        'default_price_days' => 7,
        'max_price_days' => 90,
    ],
    
    // 调试配置
    'debug' => [
        'enabled' => env('qqbot.debug_mode', false),
        'response_timeout' => (int)env('qqbot.response_timeout', 30),
    ],
];
