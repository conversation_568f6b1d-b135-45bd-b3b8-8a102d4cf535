<?php

namespace app\qqbot\middleware;

use think\facade\Config;
use think\Response;

/**
 * QQBot认证中间件
 */
class QQBotAuth
{
    /**
     * 处理请求
     */
    public function handle($request, \Closure $next)
    {
        // 检查QQBot功能是否启用
        if (!Config::get('qqbot.enabled', true)) {
            return json([
                'code' => 0,
                'msg' => 'QQBot服务已禁用',
                'data' => null
            ], 503);
        }

        // 获取请求的控制器和方法
        $controller = $request->controller();
        $action = $request->action();
        
        // 对于status接口，可以允许无认证访问
        if ($controller === 'BusinessDoc' && $action === 'status') {
            return $next($request);
        }

        // 获取Authorization头
        $authHeader = $request->header('Authorization', '');
        
        if (empty($authHeader)) {
            $this->logAuthFailure($request, 'Missing Authorization header');
            return json([
                'code' => 0,
                'msg' => '缺少认证信息',
                'data' => null
            ], 401);
        }

        // 解析Bearer Token
        if (!preg_match('/Bearer\s+(.+)/', $authHeader, $matches)) {
            $this->logAuthFailure($request, 'Invalid Authorization format');
            return json([
                'code' => 0,
                'msg' => '认证格式错误',
                'data' => null
            ], 401);
        }

        $token = $matches[1];
        
        // 验证Token
        $validTokens = Config::get('qqbot.auth_tokens', []);
        
        if (!in_array($token, $validTokens)) {
            $this->logAuthFailure($request, 'Invalid token', $token);
            return json([
                'code' => 0,
                'msg' => '认证失败',
                'data' => null
            ], 401);
        }

        // 检查IP白名单
        $ipWhitelist = Config::get('qqbot.security.ip_whitelist', '');
        if (!empty($ipWhitelist)) {
            $allowedIPs = array_map('trim', explode(',', $ipWhitelist));
            $clientIP = $request->ip();
            
            if (!in_array($clientIP, $allowedIPs)) {
                $this->logAuthFailure($request, 'IP not in whitelist', null, $clientIP);
                return json([
                    'code' => 0,
                    'msg' => 'IP访问受限',
                    'data' => null
                ], 403);
            }
        }

        // 认证成功

        return $next($request);
    }

    /**
     * 记录认证失败日志（已简化，不记录日志）
     */
    private function logAuthFailure($request, string $reason, ?string $token = null, ?string $ip = null): void
    {
        // 简化架构，不记录日志
    }
}
