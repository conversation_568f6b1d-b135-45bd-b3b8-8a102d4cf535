# QQBot应用路由配置说明

## 路由结构

基于ThinkPHP的多应用模式，新增了 `qqbot` 应用，路由结构如下：

```
/qqbot/BusinessDoc/方法名
```

## 可访问的路由

### 1. 消息处理接口
```
POST /qqbot/BusinessDoc/message
```
**功能**: 处理机器人消息的主入口  
**认证**: 需要Bearer Token  
**请求体**: JSON格式的消息数据

### 2. 物品查询接口
```
GET/POST /qqbot/BusinessDoc/items
```
**功能**: 物品搜索查询  
**认证**: 需要Bearer Token  
**参数**: keyword(关键词), pageSize(页面大小)

### 3. 价格查询接口
```
GET/POST /qqbot/BusinessDoc/price
```
**功能**: 物品价格历史查询  
**认证**: 需要Bearer Token  
**参数**: item_name(物品名), days(天数)

### 4. 排行榜查询接口
```
GET/POST /qqbot/BusinessDoc/ranking
```
**功能**: 价格排行榜查询  
**认证**: 需要Bearer Token  
**参数**: type(排行类型), limit(数量限制)

### 5. 状态检查接口
```
GET /qqbot/BusinessDoc/status
```
**功能**: 服务健康状态检查  
**认证**: 无需认证  
**返回**: 服务状态信息

### 6. 配置信息接口
```
GET /qqbot/BusinessDoc/config
```
**功能**: 获取Bot配置信息  
**认证**: 需要Bearer Token  
**返回**: 支持的命令和配置信息

## URL访问示例

假设你的域名是 `https://api.example.com`，则完整的访问路径为：

```bash
# 消息处理
POST https://api.example.com/qqbot/BusinessDoc/message

# 物品查询
GET https://api.example.com/qqbot/BusinessDoc/items?keyword=AK-74

# 价格查询
GET https://api.example.com/qqbot/BusinessDoc/price?item_name=AK-74&days=7

# 排行榜
GET https://api.example.com/qqbot/BusinessDoc/ranking?type=price_desc

# 状态检查
GET https://api.example.com/qqbot/BusinessDoc/status

# 配置信息
GET https://api.example.com/qqbot/BusinessDoc/config
```

## 认证方式

大部分接口需要在请求头中添加Bearer Token：

```bash
Authorization: Bearer your-token-here
```

Token在 `config/qqbot.php` 中配置：

```php
'auth_tokens' => [
    'your-secure-token-here',
    'backup-token-here',
],
```

## 中文路径支持

ThinkPHP框架原生支持中文路径，`业务文档` 是控制器名称，可以正常访问。

如果遇到中文路径问题，可以考虑：

1. **服务器配置**: 确保Nginx/Apache支持UTF-8编码
2. **URL编码**: 客户端可以对中文进行URL编码
3. **别名映射**: 在路由配置中添加英文别名

## 路由别名配置（可选）

如果需要英文路径，可以在应用中添加路由配置：

```php
// app/qqbot/route/app.php (需要创建)
use think\facade\Route;

// 为中文控制器添加英文别名
Route::group('business', function () {
    Route::post('message', '业务文档/message');
    Route::any('items', '业务文档/items');
    Route::any('price', '业务文档/price');
    Route::any('ranking', '业务文档/ranking');
    Route::get('status', '业务文档/status');
    Route::get('config', '业务文档/config');
});
```

这样就可以使用英文路径：
```
/qqbot/business/message
/qqbot/business/items
等等...
```

## 测试路由

可以使用以下命令测试路由是否正常：

```bash
# 测试状态接口（无需认证）
curl -X GET "https://your-domain.com/qqbot/BusinessDoc/status"

# 测试认证接口
curl -X GET "https://your-domain.com/qqbot/BusinessDoc/config" \
  -H "Authorization: Bearer your-token"

# 测试消息处理接口
curl -X POST "https://your-domain.com/qqbot/BusinessDoc/message" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"message":"/帮助","user_id":"123456","message_type":"private"}'
```

## 注意事项

1. **中文编码**: 确保服务器和客户端都使用UTF-8编码
2. **URL编码**: 某些HTTP客户端可能需要对中文路径进行URL编码
3. **大小写敏感**: 控制器名称和方法名称区分大小写
4. **路径分隔符**: 使用正斜杠 `/` 作为路径分隔符

这个路由设计既满足了你的 `/qqbot/业务文档/路径` 需求，又保持了与现有 `/api/` 路径相同的实现方式。
