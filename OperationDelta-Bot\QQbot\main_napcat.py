"""
QQ机器人 NapCat 主程序
基于 OneBot v11 协议的现代化 QQ 机器人
"""

import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from adapters.napcat import NapCatAdapter
from adapters.napcat.onebot.models import MessageEvent, OneBotEvent
from adapters.cloud_api_client import cloud_api_manager
from config.cloud_config import get_cloud_config
from utils import setup_logger, get_logger


class NapCatBotApplication:
    """基于 NapCat 的 QQ 机器人应用程序"""
    
    def __init__(self):
        # 初始化日志系统
        setup_logger()
        self.logger = get_logger(__name__)
        
        # 核心组件
        self.adapter: NapCatAdapter = None

        # 应用状态
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        self.logger.info("NapCat QQ机器人应用程序初始化完成")
    
    async def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            self.logger.info("正在初始化 NapCat QQ机器人应用程序...")
            
            # 初始化云端API
            cloud_config = get_cloud_config()
            if cloud_config.enabled:
                self.logger.info("正在初始化云端API...")
                cloud_api_config = {
                    'enabled': cloud_config.enabled,
                    'base_url': cloud_config.base_url,
                    'auth_token': cloud_config.auth_token,
                    'timeout': cloud_config.timeout,
                    'max_retries': cloud_config.max_retries,
                    'command_prefixes': cloud_config.command_prefixes
                }
                
                if await cloud_api_manager.initialize(cloud_api_config):
                    self.logger.info("云端API初始化成功")
                else:
                    self.logger.error("云端API初始化失败，程序退出")
                    return False
            else:
                self.logger.info("云端API功能已禁用")
            
            # 创建 NapCat 适配器
            self.adapter = NapCatAdapter()
            
            # 注册事件处理器
            self._register_event_handlers()

            self.logger.info("NapCat QQ机器人应用程序初始化成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}")
            return False
    
    def _register_event_handlers(self):
        """注册事件处理器"""
        try:
            # 注册消息事件处理器
            self.adapter.client.on("message", self._on_message)
            self.adapter.client.on("message.private", self._on_private_message)
            self.adapter.client.on("message.group", self._on_group_message)
            
            # 注册通知事件处理器
            self.adapter.client.on("notice", self._on_notice)
            self.adapter.client.on("notice.group_increase", self._on_group_member_join)
            self.adapter.client.on("notice.group_decrease", self._on_group_member_leave)
            
            # 注册请求事件处理器
            self.adapter.client.on("request", self._on_request)
            self.adapter.client.on("request.friend", self._on_friend_request)
            self.adapter.client.on("request.group", self._on_group_request)
            
            # 注册元事件处理器
            self.adapter.client.on("meta_event", self._on_meta_event)
            self.adapter.client.on("meta_event.heartbeat", self._on_heartbeat)
            
            self.logger.info("事件处理器注册完成")
            
        except Exception as e:
            self.logger.error(f"事件处理器注册失败: {e}")
    

    


    # 事件处理器
    async def _on_message(self, event: MessageEvent):
        """处理所有消息事件"""
        try:
            self.logger.debug(f"收到消息事件: {self.adapter.client.parser.get_event_summary(event)}")
        except Exception as e:
            self.logger.error(f"处理消息事件失败: {e}")
    
    async def _on_private_message(self, event: MessageEvent):
        """处理私聊消息"""
        try:
            # 显示包含艾特信息的完整消息
            message_text = event.message.extract_plain_text(include_at=True)
            self.logger.info(f"收到私聊消息: {event.sender.get('nickname', '未知用户')}({event.user_id}): {message_text}")

            # 云端API处理
            if cloud_api_manager.is_available():
                reply = await cloud_api_manager.process_message(event, 'private')
                if reply:
                    await self.adapter.client.send_message(
                        message_type="private",
                        target_id=event.user_id,
                        message=reply
                    )
                    self.logger.info(f"云端API回复私聊消息: {reply[:50]}...")
                else:
                    self.logger.info("云端API未返回回复")
            else:
                self.logger.warning("云端API不可用，消息未处理")

        except Exception as e:
            self.logger.error(f"处理私聊消息失败: {e}")

    async def _on_group_message(self, event: MessageEvent):
        """处理群消息"""
        try:
            # 显示包含艾特信息的完整消息
            message_text = event.message.extract_plain_text(include_at=True)
            self.logger.info(f"收到群消息: 群{event.group_id} - {event.sender.get('nickname', '未知用户')}({event.user_id}): {message_text}")

            # 云端API处理
            if cloud_api_manager.is_available():
                reply = await cloud_api_manager.process_message(event, 'group')
                if reply:
                    await self.adapter.client.send_message(
                        message_type="group",
                        target_id=event.group_id,
                        message=reply
                    )
                    self.logger.info(f"云端API回复群消息: {reply[:50]}...")
                else:
                    self.logger.info("云端API未返回回复")
            else:
                self.logger.warning("云端API不可用，消息未处理")

        except Exception as e:
            self.logger.error(f"处理群消息失败: {e}")





    async def _on_notice(self, event: OneBotEvent):
        """处理通知事件"""
        try:
            self.logger.info(f"收到通知事件: {self.adapter.client.parser.get_event_summary(event)}")
        except Exception as e:
            self.logger.error(f"处理通知事件失败: {e}")

    async def _on_group_member_join(self, event: OneBotEvent):
        """处理群成员加入"""
        try:
            self.logger.info(f"群成员加入: 群{event.group_id} - 用户{event.user_id}")
        except Exception as e:
            self.logger.error(f"处理群成员加入事件失败: {e}")

    async def _on_group_member_leave(self, event: OneBotEvent):
        """处理群成员离开"""
        try:
            self.logger.info(f"群成员离开: 群{event.group_id} - 用户{event.user_id}")
        except Exception as e:
            self.logger.error(f"处理群成员离开事件失败: {e}")

    async def _on_request(self, event: OneBotEvent):
        """处理请求事件"""
        try:
            self.logger.info(f"收到请求事件: {self.adapter.client.parser.get_event_summary(event)}")
        except Exception as e:
            self.logger.error(f"处理请求事件失败: {e}")
    
    async def _on_friend_request(self, event: OneBotEvent):
        """处理好友申请"""
        try:
            self.logger.info(f"收到好友申请: 用户{event.user_id} - {event.comment}")
            # 云端转发器不处理好友申请，仅记录日志
        except Exception as e:
            self.logger.error(f"处理好友申请失败: {e}")

    async def _on_group_request(self, event: OneBotEvent):
        """处理群申请/邀请"""
        try:
            self.logger.info(f"收到群申请/邀请: 群{event.group_id} - 用户{event.user_id}")
            # 云端转发器不处理群申请，仅记录日志
        except Exception as e:
            self.logger.error(f"处理群申请/邀请失败: {e}")

    async def _on_meta_event(self, event: OneBotEvent):
        """处理元事件"""
        try:
            # 元事件通常是心跳等，使用 debug 级别
            self.logger.debug(f"收到元事件: {self.adapter.client.parser.get_event_summary(event)}")
        except Exception as e:
            self.logger.error(f"处理元事件失败: {e}")

    async def _on_heartbeat(self, event: OneBotEvent):
        """处理心跳事件"""
        try:
            self.logger.debug(f"收到心跳事件: {self.adapter.client.parser.get_event_summary(event)}")
        except Exception as e:
            self.logger.error(f"处理心跳事件失败: {e}")
    
    async def run(self):
        """运行应用程序"""
        try:
            self.is_running = True
            self.logger.info("NapCat QQ机器人应用程序启动")
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            # 运行适配器
            adapter_task = asyncio.create_task(self.adapter.start())
            
            # 等待关闭信号
            shutdown_task = asyncio.create_task(self.shutdown_event.wait())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                [adapter_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # 检查适配器任务是否有异常
            for task in done:
                if task.exception():
                    self.logger.error(f"任务异常: {task.exception()}")
            
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"应用程序运行异常: {e}")
        finally:
            await self.shutdown()
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        if sys.platform != 'win32':
            # Unix系统信号处理
            loop = asyncio.get_event_loop()
            
            def signal_handler():
                self.logger.info("收到关闭信号")
                self.shutdown_event.set()
            
            loop.add_signal_handler(signal.SIGINT, signal_handler)
            loop.add_signal_handler(signal.SIGTERM, signal_handler)
        else:
            # Windows系统使用不同的方式
            def signal_handler(signum, _):
                self.logger.info(f"收到信号 {signum}")
                asyncio.create_task(self._async_shutdown())
            
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)
    
    async def _async_shutdown(self):
        """异步关闭"""
        self.shutdown_event.set()
    
    async def shutdown(self):
        """关闭应用程序"""
        try:
            self.is_running = False
            self.logger.info("正在关闭 NapCat QQ机器人应用程序...")

                        # 停止适配器
            if self.adapter:
                await self.adapter.stop()
            
            # 关闭云端API客户端
            await cloud_api_manager.close()

            self.logger.info("NapCat QQ机器人应用程序已关闭")

        except Exception as e:
            self.logger.error(f"关闭应用程序时发生错误: {e}")
    
    def get_status(self) -> dict:
        """获取应用程序状态"""
        status = {
            'is_running': self.is_running,
            'adapter_status': self.adapter.get_status() if self.adapter else None,
            'cloud_api_available': cloud_api_manager.is_available()
        }

        return status


async def main():
    """主函数"""
    app = NapCatBotApplication()
    
    try:
        # 初始化应用程序
        if not await app.initialize():
            print("应用程序初始化失败")
            sys.exit(1)
        
        # 运行应用程序
        await app.run()
        
    except Exception as e:
        print(f"应用程序运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 设置事件循环策略（Windows）
    if sys.platform == 'win32':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行主程序
    asyncio.run(main())
