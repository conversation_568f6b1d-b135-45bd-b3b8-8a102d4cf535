<?php

namespace app\qqbot\controller;

use app\common\controller\Frontend;
use app\api\service\ResponseAdapter;
use think\facade\Config;
use think\exception\HttpResponseException;

/**
 * QQBot应用基础控制器
 * 提供统一的认证、日志和响应处理
 */
abstract class BaseController extends Frontend
{
    protected array $noNeedLogin = [];
    protected array $noNeedPermission = [];

    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        
        // QQBot应用初始化
        $this->initializeQQBotApp();
    }

    /**
     * 初始化QQBot应用
     */
    private function initializeQQBotApp(): void
    {
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        

    }

    /**
     * 统一成功响应 (已弃用，请直接使用ResponseAdapter)
     */
    protected function success(string $msg = '', mixed $data = null, int $code = 1, string $type = null, array $header = [], array $options = []): void
    {
        throw new HttpResponseException(ResponseAdapter::success($msg ?: '操作成功', $data));
    }

    /**
     * 统一错误响应 (已弃用，请直接使用ResponseAdapter)
     */
    protected function error(string $msg = '', mixed $data = null, int $code = 0, string $type = null, array $header = [], array $options = []): void
    {
        throw new HttpResponseException(ResponseAdapter::error($msg ?: '操作失败', $data));
    }

    /**
     * Token认证检查
     */
    protected function checkAuth(): bool
    {
        $token = $this->request->header('Authorization', '');
        
        if (empty($token)) {
            return false;
        }

        // 移除Bearer前缀
        $token = str_replace('Bearer ', '', $token);
        
        // 验证Token
        $validTokens = Config::get('qqbot.auth_tokens', []);
        
        return in_array($token, $validTokens);
    }


}
