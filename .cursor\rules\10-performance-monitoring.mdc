---
globs: OperationDelta-Admin_Api_new/app/api/service/PerformanceMonitor.php,**/monitoring/**,**/performance/**
description: 性能监控规则（性能指标、监控工具、优化策略）
---

### 性能监控架构

基于 [OperationDelta-Admin_Api_new/app/api/service/PerformanceMonitor.php](mdc:OperationDelta-Admin_Api_new/app/api/service/PerformanceMonitor.php) 的监控体系：

核心功能：
- 请求计时和响应时间统计
- 数据库查询性能监控
- 内存使用情况跟踪
- API 调用频率统计
- 慢查询检测和告警

### 关键性能指标（KPI）

前端性能指标：
- **FCP (First Contentful Paint)**：首次内容绘制时间 < 1.5s
- **LCP (Largest Contentful Paint)**：最大内容绘制时间 < 2.5s
- **FID (First Input Delay)**：首次输入延迟 < 100ms
- **CLS (Cumulative Layout Shift)**：累积布局偏移 < 0.1
- **TTFB (Time to First Byte)**：首字节时间 < 600ms

后端性能指标：
- **API 响应时间**：平均响应时间 < 200ms，95% 请求 < 500ms
- **数据库查询时间**：平均查询时间 < 50ms，慢查询 > 1s
- **吞吐量**：每秒请求数（QPS/TPS）
- **错误率**：4xx/5xx 错误率 < 1%
- **可用性**：系统正常运行时间 > 99.9%

系统资源指标：
- **CPU 使用率**：平均 < 70%，峰值 < 90%
- **内存使用率**：< 80%
- **磁盘 I/O**：队列长度 < 10
- **网络带宽**：使用率 < 80%

### 前端性能监控

Web 应用监控（[OperationDelta-Web](mdc:OperationDelta-Web)）：
- 使用 Web Vitals API 收集核心指标
- Performance Observer 监控资源加载
- 自定义埋点监控关键用户操作
- 错误监控和异常捕获

```javascript
// 性能监控示例
const performanceObserver = new PerformanceObserver((list) => {
  for (const entry of list.getEntries()) {
    if (entry.entryType === 'navigation') {
      console.log('Page Load Time:', entry.loadEventEnd - entry.fetchStart);
    }
  }
});
performanceObserver.observe({ entryTypes: ['navigation'] });
```

Desktop 应用监控（[OperationDelta-Desktop](mdc:OperationDelta-Desktop)）：
- Electron 主进程和渲染进程性能监控
- 内存泄漏检测
- 启动时间和应用响应时间
- 崩溃报告和错误追踪

小程序性能监控（[OperationDelta-xcx](mdc:OperationDelta-xcx)）：
- 小程序性能分析工具
- 页面渲染时间监控
- 网络请求性能分析
- 用户操作响应时间

### 后端性能监控

PHP 应用监控：
使用 PerformanceMonitor 服务进行监控：

```php
// 控制器中使用性能监控
use app\api\service\PerformanceMonitor;

class EconomicsHistoryController extends BaseController
{
    public function index()
    {
        $monitor = PerformanceMonitor::getInstance();
        
        // 开始计时
        $monitor->startTimer('economics_history_query');
        
        // 业务逻辑
        $result = $this->service->getHistoryData($params);
        
        // 结束计时
        $monitor->endTimer('economics_history_query');
        
        // 记录性能指标
        $monitor->recordMetric('api_call_count', 1);
        
        return ResponseAdapter::success('获取成功', $result);
    }
}
```

数据库性能监控：
- 慢查询日志分析
- 查询执行计划监控
- 数据库连接池状态
- 索引使用效率分析

缓存性能监控：
- Redis/Memcached 命中率
- 缓存响应时间
- 内存使用情况
- 缓存失效策略效果

### 实时监控工具

应用性能监控（APM）工具：
- **New Relic**：全栈应用性能监控
- **DataDog**：基础设施和应用监控
- **Elastic APM**：开源 APM 解决方案
- **Sentry**：错误追踪和性能监控

系统监控工具：
- **Prometheus + Grafana**：指标收集和可视化
- **ELK Stack**：日志收集和分析
- **Nagios/Zabbix**：基础设施监控
- **Jaeger**：分布式追踪

### 告警机制

告警规则配置：
```yaml
# 示例告警规则
rules:
  - name: api_response_time
    condition: avg_response_time > 500ms
    duration: 5m
    severity: warning
    notification:
      - email: <EMAIL>
      - webhook: https://slack.webhook.url

  - name: error_rate_high
    condition: error_rate > 5%
    duration: 2m
    severity: critical
    notification:
      - sms: +86-1234567890
      - email: <EMAIL>
```

告警分级：
- **Critical**：系统不可用，立即处理
- **Warning**：性能下降，需要关注
- **Info**：信息通知，记录备查

### 性能优化策略

前端优化：
- **资源优化**：代码分割、Tree Shaking、压缩
- **缓存策略**：浏览器缓存、CDN 缓存、Service Worker
- **懒加载**：图片懒加载、路由懒加载、组件懒加载
- **预加载**：关键资源预加载、DNS 预解析

后端优化：
- **数据库优化**：索引优化、查询优化、连接池
- **缓存策略**：多级缓存、缓存穿透防护
- **代码优化**：算法优化、异步处理、并发控制
- **架构优化**：负载均衡、微服务拆分

网络优化：
- **HTTP/2**：多路复用、服务器推送
- **CDN**：全球内容分发网络
- **压缩**：Gzip/Brotli 压缩
- **Keep-Alive**：连接复用

### 性能测试

负载测试：
- **压力测试**：测试系统极限性能
- **容量测试**：确定系统最大容量
- **稳定性测试**：长时间运行测试
- **峰值测试**：模拟流量峰值

测试工具：
- **Apache JMeter**：Web 应用性能测试
- **Artillery**：现代化负载测试工具
- **k6**：开发者友好的性能测试
- **Lighthouse**：Web 性能自动化测试

### 性能分析报告

定期性能报告内容：
- **性能趋势分析**：关键指标变化趋势
- **瓶颈识别**：性能瓶颈点分析
- **优化建议**：具体的优化措施
- **成本效益分析**：优化投入产出比

报告生成：
- 自动化报告生成
- 数据可视化展示
- 历史数据对比
- 预测性分析

### 用户体验监控

Real User Monitoring (RUM)：
- 真实用户访问性能数据
- 地理位置分布分析
- 设备和浏览器性能差异
- 用户行为路径分析

业务指标监控：
- **转化率**：关键流程转化情况
- **跳出率**：页面跳出率分析
- **用户满意度**：性能对用户体验的影响
- **业务影响**：性能问题对业务的影响

### 监控数据治理

数据收集规范：
- 指标命名规范
- 数据格式标准化
- 采样率配置
- 数据保留策略

数据质量保证：
- 数据准确性验证
- 异常数据过滤
- 数据完整性检查
- 监控数据的监控

### 成本优化

监控成本控制：
- 合理设置采样率
- 优化数据存储策略
- 智能告警减少噪音
- 工具选型成本考虑

ROI 分析：
- 监控投入成本分析
- 性能优化带来的收益
- 故障预防价值评估
- 用户体验提升价值

### 团队协作

监控责任分工：
- **开发团队**：应用层性能监控和优化
- **运维团队**：基础设施监控和告警
- **测试团队**：性能测试和验证
- **业务团队**：业务指标监控和分析

知识共享：
- 性能优化最佳实践
- 监控工具使用培训
- 故障处理经验分享
- 新技术评估和引入