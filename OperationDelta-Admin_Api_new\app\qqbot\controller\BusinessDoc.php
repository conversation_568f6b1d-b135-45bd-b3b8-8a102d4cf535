<?php

namespace app\qqbot\controller;

use app\qqbot\service\MessageProcessor;
use app\qqbot\service\MessageFormatter;
use app\api\service\ResponseAdapter;

/**
 * QQBot业务文档控制器
 * 处理机器人的核心业务逻辑
 */
class BusinessDoc extends BaseController
{
    protected array $noNeedLogin = ['message', 'status', 'config'];
    
    private MessageProcessor $messageProcessor;
    private MessageFormatter $messageFormatter;

    public function __construct(\think\App $app)
    {
        parent::__construct($app);
        $this->messageProcessor = new MessageProcessor();
        $this->messageFormatter = new MessageFormatter();
    }

    /**
     * 处理机器人消息的主入口
     * 路由: /qqbot/BusinessDoc/message
     */
    public function message()
    {
        try {
            // 验证认证
            if (!$this->checkAuth()) {
                return ResponseAdapter::forbidden('认证失败');
            }

            // 获取请求数据
            $requestData = $this->getRequestData();
            if (!$requestData) {
                return ResponseAdapter::validationError('请求数据格式错误');
            }

            // 验证必要字段
            $validation = $this->validateMessageData($requestData);
            if (!$validation['valid']) {
                return ResponseAdapter::validationError($validation['message']);
            }

            // 处理消息
            $result = $this->messageProcessor->process($requestData);



            return ResponseAdapter::success('处理成功', $result);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('服务器内部错误');
        }
    }

    /**
     * 物品查询接口
     * 路由: /qqbot/BusinessDoc/items
     */
    public function items()
    {
        try {
            if (!$this->checkAuth()) {
                return ResponseAdapter::forbidden('认证失败');
            }

            $keyword = $this->request->param('keyword', '');
            $pageSize = $this->request->param('pageSize', 10);
            
            if (empty($keyword)) {
                return ResponseAdapter::validationError('搜索关键词不能为空');
            }

            // 获取物品数据
            $itemsData = $this->getItemsData($keyword, $pageSize);
            
            // 格式化为QQBot消息格式
            $formattedMessage = $this->messageFormatter->formatItemList($itemsData, $keyword);

            return ResponseAdapter::success('查询成功', [
                'reply' => $formattedMessage,
                'reply_type' => 'text',
                'should_reply' => true
            ]);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('查询失败');
        }
    }

    /**
     * 价格查询接口
     * 路由: /qqbot/BusinessDoc/price
     */
    public function price()
    {
        try {
            if (!$this->checkAuth()) {
                return ResponseAdapter::forbidden('认证失败');
            }

            $itemName = $this->request->param('item_name', '');
            $days = $this->request->param('days', 7);
            
            if (empty($itemName)) {
                return ResponseAdapter::validationError('物品名称不能为空');
            }

            // 调用价格查询逻辑
            $priceData = $this->getPriceData($itemName, $days);
            
            // 格式化消息
            $formattedMessage = $this->messageFormatter->formatPriceHistory($priceData, $itemName);

            return ResponseAdapter::success('查询成功', [
                'reply' => $formattedMessage,
                'reply_type' => 'text',
                'should_reply' => true
            ]);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('价格查询失败');
        }
    }

    /**
     * 排行榜查询接口
     * 路由: /qqbot/BusinessDoc/ranking
     */
    public function ranking()
    {
        try {
            if (!$this->checkAuth()) {
                return ResponseAdapter::forbidden('认证失败');
            }

            $type = $this->request->param('type', 'price_desc');
            $limit = $this->request->param('limit', 10);

            // 调用排行榜逻辑
            $rankingData = $this->getRankingData($type, $limit);
            
            // 格式化消息
            $formattedMessage = $this->messageFormatter->formatRanking($rankingData, $type);

            return ResponseAdapter::success('查询成功', [
                'reply' => $formattedMessage,
                'reply_type' => 'text',
                'should_reply' => true
            ]);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('排行榜查询失败');
        }
    }

    /**
     * 状态检查接口
     * 路由: /qqbot/BusinessDoc/status
     */
    public function status()
    {
        try {
            $status = [
                'timestamp' => time(),
                'datetime' => date('Y-m-d H:i:s'),
                'status' => 'healthy',
                'version' => '1.0.0',
                'services' => $this->getServiceStatus()
            ];

            return ResponseAdapter::success('服务正常', $status);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('服务异常');
        }
    }

    /**
     * 配置信息接口
     * 路由: /qqbot/BusinessDoc/config
     */
    public function config()
    {
        try {
            if (!$this->checkAuth()) {
                return ResponseAdapter::forbidden('认证失败');
            }

            $config = [
                'commands' => $this->messageProcessor->getSupportedCommands(),
                'limits' => [
                    'max_message_length' => 4000,
                    'max_results_per_query' => 10
                ],
                'features' => [
                    'enable_image' => false,
                    'enable_at' => true
                ]
            ];

            return ResponseAdapter::success('获取配置成功', $config);

        } catch (\Exception $e) {

            return ResponseAdapter::serverError('获取配置失败');
        }
    }

    /**
     * 获取请求数据
     */
    private function getRequestData(): ?array
    {
        try {
            $contentType = $this->request->header('Content-Type', '');
            
            if (strpos($contentType, 'application/json') !== false) {
                $rawData = $this->request->getContent();
                $data = json_decode($rawData, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return null;
                }
                
                return $data;
            } else {
                return $this->request->post();
            }

        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 验证消息数据
     */
    private function validateMessageData(array $data): array
    {
        $requiredFields = ['message_type', 'user_id', 'message'];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                return [
                    'valid' => false,
                    'message' => "缺少必要字段: {$field}"
                ];
            }
        }

        if (!in_array($data['message_type'], ['group', 'private'])) {
            return [
                'valid' => false,
                'message' => '无效的消息类型'
            ];
        }

        if ($data['message_type'] === 'group' && empty($data['group_id'])) {
            return [
                'valid' => false,
                'message' => '群消息缺少group_id'
            ];
        }

        return ['valid' => true];
    }

    /**
     * 获取物品数据（占位方法，需要根据实际业务逻辑实现）
     */
    private function getItemsData(string $keyword, int $pageSize): array
    {
        // 这里需要调用现有的Items控制器逻辑
        // 暂时返回示例数据
        return [
            'items' => [],
            'total' => 0
        ];
    }

    /**
     * 获取价格数据（占位方法）
     */
    private function getPriceData(string $itemName, int $days): array
    {
        // 这里需要调用现有的价格查询逻辑
        return [
            'history' => [],
            'statistics' => []
        ];
    }

    /**
     * 获取排行榜数据（占位方法）
     */
    private function getRankingData(string $type, int $limit): array
    {
        // 这里需要调用现有的排行榜逻辑
        return [
            'list' => []
        ];
    }

    /**
     * 获取服务状态
     */
    private function getServiceStatus(): array
    {
        $status = [];

        try {
            // 检查数据库
            \think\facade\Db::query('SELECT 1');
            $status['database'] = 'healthy';
        } catch (\Exception $e) {
            $status['database'] = 'error';
        }

        try {
            // 检查Redis
            \think\facade\Cache::set('health_check', time(), 10);
            $status['redis'] = 'healthy';
        } catch (\Exception $e) {
            $status['redis'] = 'error';
        }

        return $status;
    }
}
