<?php

namespace app\qqbot\service;
use think\App;


/**
 * 消息处理器
 * 负责解析指令、路由到对应的处理方法
 */
class MessageProcessor
{
    private array $supportedCommands;
    private MessageFormatter $formatter;

    public function __construct()
    {
        $this->formatter = new MessageFormatter();
        $this->initSupportedCommands();
    }

    /**
     * 处理消息
     */
    public function process(array $messageData): array
    {
        $message = trim($messageData['message']);
        $command = $this->parseCommand($message);

        try {
            $result = $this->executeCommand($command, $messageData);
            
            return [
                'should_reply' => true,
                'reply_type' => $result['type'] ?? 'text',
                'reply' => $result['content'],
                'command' => $command['command'],
                'execution_time_ms' => $result['execution_time_ms'] ?? 0
            ];

        } catch (\Exception $e) {

            return [
                'should_reply' => true,
                'reply_type' => 'text',
                'reply' => $this->formatter->formatError('命令执行失败，请稍后重试'),
                'command' => $command['command'] ?? 'unknown',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 解析命令
     */
    private function parseCommand(string $message): array
    {
        $message = trim($message);
        
        // 移除指令前缀
        $cleanMessage = $this->removeCommandPrefix($message);
        
        // 按空格分割
        $parts = array_filter(explode(' ', $cleanMessage));
        
        if (empty($parts)) {
            return ['command' => 'unknown', 'args' => []];
        }

        $mainCommand = $parts[0];
        $args = array_slice($parts, 1);

        // 映射到标准命令
        $command = $this->mapToStandardCommand($mainCommand);

        return [
            'command' => $command,
            'raw_command' => $mainCommand,
            'args' => $args,
            'full_message' => $message
        ];
    }

    /**
     * 移除指令前缀
     */
    private function removeCommandPrefix(string $message): string
    {
        $prefixes = ['/', '/', '!', '！'];
        
        foreach ($prefixes as $prefix) {
            if (strpos($message, $prefix) === 0) {
                return substr($message, strlen($prefix));
            }
        }
        
        return $message;
    }

    /**
     * 映射到标准命令
     */
    private function mapToStandardCommand(string $rawCommand): string
    {
        $commandMap = [
            // 物品相关
            '物品' => 'item_search',
            '搜索' => 'item_search',
            'item' => 'item_search',
            'search' => 'item_search',
            
            // 价格相关
            '价格' => 'price_query',
            'price' => 'price_query',
            '价格查询' => 'price_query',
            
            // 排行榜
            '排行榜' => 'ranking',
            '排行' => 'ranking',
            'ranking' => 'ranking',
            'rank' => 'ranking',
            
            // 地图密码
            '今日密码' => 'daily_password',
            '地图密码' => 'daily_password',
            '密码' => 'daily_password',
            'password' => 'daily_password',
            
            // 特勤处
            '特勤处' => 'special_operation',
            '特勤' => 'special_operation',
            'special' => 'special_operation',
            
            // 帮助
            '帮助' => 'help',
            'help' => 'help',
            '使用说明' => 'help',
            '指令' => 'help'
        ];

        return $commandMap[strtolower($rawCommand)] ?? 'unknown';
    }

    /**
     * 执行命令
     */
    private function executeCommand(array $command, array $messageData): array
    {
        $startTime = microtime(true);
        
        switch ($command['command']) {
            case 'item_search':
                $result = $this->handleItemSearch($command['args']);
                break;
                
            case 'price_query':
                $result = $this->handlePriceQuery($command['args']);
                break;
                
            case 'ranking':
                $result = $this->handleRanking($command['args']);
                break;
                
            case 'daily_password':
                $result = $this->handleDailyPassword();
                break;
                
            case 'special_operation':
                $result = $this->handleSpecialOperation($command['args']);
                break;
                
            case 'help':
                $result = $this->handleHelp();
                break;
                
            default:
                $result = $this->handleUnknownCommand($command);
        }

        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        $result['execution_time_ms'] = $executionTime;

        return $result;
    }

    /**
     * 处理物品搜索
     */
    private function handleItemSearch(array $args): array
    {
        if (empty($args)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatUsage('物品搜索', '/物品 <关键词>')
            ];
        }

        $keyword = implode(' ', $args);
        
        // 这里应该调用实际的业务逻辑
        // 暂时返回示例响应
        return [
            'type' => 'text',
            'content' => $this->formatter->formatItemList([], $keyword)
        ];
    }

    /**
     * 处理价格查询
     */
    private function handlePriceQuery(array $args): array
    {
        if (empty($args)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatUsage('价格查询', '/价格 <物品名> [天数]')
            ];
        }

        // 解析物品名和天数
        $itemName = '';
        $days = 7;
        
        foreach ($args as $arg) {
            if (is_numeric($arg)) {
                $days = intval($arg);
            } else {
                $itemName .= $arg . ' ';
            }
        }
        
        $itemName = trim($itemName);
        
        if (empty($itemName)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatError('请提供物品名称')
            ];
        }

        // 这里应该调用实际的业务逻辑
        return [
            'type' => 'text',
            'content' => $this->formatter->formatPriceHistory([], $itemName)
        ];
    }

    /**
     * 处理排行榜查询
     */
    private function handleRanking(array $args): array
    {
        $rankingType = 'price_desc'; // 默认最贵排行
        
        if (!empty($args)) {
            $typeMap = [
                '最贵' => 'price_desc',
                '最便宜' => 'price_asc',
                '涨幅' => 'change_desc',
                '跌幅' => 'change_asc'
            ];
            
            $argStr = implode(' ', $args);
            foreach ($typeMap as $key => $type) {
                if (strpos($argStr, $key) !== false) {
                    $rankingType = $type;
                    break;
                }
            }
        }

        // 这里应该调用实际的业务逻辑
        return [
            'type' => 'text',
            'content' => $this->formatter->formatRanking([], $rankingType)
        ];
    }

    /**
     * 处理今日密码查询
     */
    private function handleDailyPassword(): array
    {
        // 这里应该调用实际的业务逻辑
        $passwordData = [
            '农场' => 'ABC123',
            '工厂' => 'DEF456',
            '研究所' => 'GHI789'
        ];
        
        return [
            'type' => 'text',
            'content' => $this->formatter->formatDailyPassword($passwordData)
        ];
    }

    /**
     * 处理特勤处查询
     */
    private function handleSpecialOperation(array $args): array
    {
        try {
            // 构建请求参数
            $params = [
                'page' => 1,
                'page_size' => 5, // QQ消息限制，只显示前5个最优操作
                'sort_by' => 'profit_per_hour',
                'sort_order' => 'desc'
            ];

            // 如果有位置参数，添加位置筛选
            if (!empty($args)) {
                $location = implode(' ', $args);
                // 映射常见的位置别名
                $locationMap = [
                    '工作台' => '工作台',
                    '技术中心' => '技术中心',
                    '制药台' => '制药台',
                    '防具' => '防具工作台'
                ];

                foreach ($locationMap as $alias => $realLocation) {
                    if (strpos($location, $alias) !== false) {
                        $params['location'] = $realLocation;
                        break;
                    }
                }
            }

            // 直接实例化控制器并调用专用方法
            $app = new App();
            $specialOperationController = new \app\api\controller\SpecialOperation($app);

            // 直接调用控制器的数据获取方法
            $data = $specialOperationController->getSpecialOperationData($params);

            // 检查是否有错误
            if (isset($data['error']) && $data['error']) {
                return [
                    'type' => 'text',
                    'content' => "🔧 特勤处查询\n\n❌ " . ($data['error_message'] ?? '获取数据失败，请稍后重试')
                ];
            }

            if (!empty($data['list'])) {
                return [
                    'type' => 'text',
                    'content' => $this->formatter->formatSpecialOperation($data['list'])
                ];
            } else {
                return [
                    'type' => 'text',
                    'content' => $this->formatter->formatSpecialOperation([])
                ];
            }

        } catch (\Exception $e) {
            // 记录详细错误日志
            \think\facade\Log::error('特勤处查询失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());

            return [
                'type' => 'text',
                'content' => "🔧 特勤处查询\n\n❌ 获取数据失败，请稍后重试\n\n错误信息: " . $e->getMessage()
            ];
        }
    }

    /**
     * 处理帮助命令
     */
    private function handleHelp(): array
    {
        return [
            'type' => 'text',
            'content' => $this->formatter->formatHelp($this->supportedCommands)
        ];
    }

    /**
     * 处理未知命令
     */
    private function handleUnknownCommand(array $command): array
    {
        return [
            'type' => 'text',
            'content' => $this->formatter->formatUnknownCommand($command['raw_command'] ?? '')
        ];
    }

    /**
     * 初始化支持的命令列表
     */
    private function initSupportedCommands(): void
    {
        $this->supportedCommands = [
            'item_search' => [
                'name' => '物品搜索',
                'aliases' => ['物品', '搜索', 'item', 'search'],
                'usage' => '/物品 <关键词>',
                'description' => '搜索游戏物品信息'
            ],
            'price_query' => [
                'name' => '价格查询',
                'aliases' => ['价格', 'price'],
                'usage' => '/价格 <物品名> [天数]',
                'description' => '查询物品价格历史'
            ],
            'ranking' => [
                'name' => '排行榜',
                'aliases' => ['排行榜', '排行', 'ranking'],
                'usage' => '/排行榜 [类型]',
                'description' => '查看价格排行榜'
            ],
            'daily_password' => [
                'name' => '今日密码',
                'aliases' => ['今日密码', '地图密码', '密码'],
                'usage' => '/今日密码',
                'description' => '获取地图通行密码'
            ],
            'special_operation' => [
                'name' => '特勤处',
                'aliases' => ['特勤处', '特勤'],
                'usage' => '/特勤处 [位置]',
                'description' => '查看特勤处操作信息'
            ],
            'help' => [
                'name' => '帮助',
                'aliases' => ['帮助', 'help'],
                'usage' => '/帮助',
                'description' => '显示使用帮助'
            ]
        ];
    }

    /**
     * 获取支持的命令列表
     */
    public function getSupportedCommands(): array
    {
        return $this->supportedCommands;
    }
}
