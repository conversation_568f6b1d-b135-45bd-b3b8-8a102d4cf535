<?php

namespace app\qqbot\service;

class MessageProcessor
{
    private array $supportedCommands;
    private MessageFormatter $formatter;

    public function __construct()
    {
        $this->formatter = new MessageFormatter();
        $this->initSupportedCommands();
    }

    public function process(array $messageData): array
    {
        $message = trim($messageData['message']);
        $command = $this->parseCommand($message);

        try {
            $result = $this->executeCommand($command, $messageData);

            return [
                'should_reply' => true,
                'reply_type' => $result['type'] ?? 'text',
                'reply' => $result['content'],
                'command' => $command['command'],
                'execution_time_ms' => $result['execution_time_ms'] ?? 0
            ];

        } catch (\Exception $e) {
            \think\facade\Log::error('MessageProcessor处理失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());

            return [
                'should_reply' => true,
                'reply_type' => 'text',
                'reply' => $this->formatter->formatError('命令执行失败，请稍后重试'),
                'command' => $command['command'] ?? 'unknown',
                'error' => $e->getMessage()
            ];
        }
    }

    private function parseCommand(string $message): array
    {
        $message = trim($message);
        $cleanMessage = $this->removeCommandPrefix($message);
        $parts = array_filter(explode(' ', $cleanMessage));

        if (empty($parts)) {
            return ['command' => 'unknown', 'args' => []];
        }

        $mainCommand = $parts[0];
        $args = array_slice($parts, 1);
        $command = $this->mapToStandardCommand($mainCommand);

        return [
            'command' => $command,
            'raw_command' => $mainCommand,
            'args' => $args,
            'full_message' => $message
        ];
    }

    private function removeCommandPrefix(string $message): string
    {
        $prefixes = ['/', '/', '!', '！'];

        foreach ($prefixes as $prefix) {
            if (strpos($message, $prefix) === 0) {
                return substr($message, strlen($prefix));
            }
        }

        return $message;
    }

    private function mapToStandardCommand(string $rawCommand): string
    {
        $commandMap = [
            '物品' => 'item_search',
            '搜索' => 'item_search',
            'item' => 'item_search',
            'search' => 'item_search',
            '价格' => 'price_query',
            'price' => 'price_query',
            '价格查询' => 'price_query',
            '排行榜' => 'ranking',
            '排行' => 'ranking',
            'ranking' => 'ranking',
            'rank' => 'ranking',
            '今日密码' => 'daily_password',
            '地图密码' => 'daily_password',
            '密码' => 'daily_password',
            'password' => 'daily_password',
            '特勤处' => 'special_operation',
            '特勤' => 'special_operation',
            'special' => 'special_operation',
            '帮助' => 'help',
            'help' => 'help',
            '使用说明' => 'help',
            '指令' => 'help'
        ];

        return $commandMap[strtolower($rawCommand)] ?? 'unknown';
    }

    private function executeCommand(array $command, array $messageData): array
    {
        $startTime = microtime(true);

        switch ($command['command']) {
            case 'item_search':
                $result = $this->handleItemSearch($command['args']);
                break;
            case 'price_query':
                $result = $this->handlePriceQuery($command['args']);
                break;
            case 'ranking':
                $result = $this->handleRanking($command['args']);
                break;
            case 'daily_password':
                $result = $this->handleDailyPassword();
                break;
            case 'special_operation':
                $result = $this->handleSpecialOperation($command['args']);
                break;
            case 'help':
                $result = $this->handleHelp();
                break;
            default:
                $result = $this->handleUnknownCommand($command);
        }

        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        $result['execution_time_ms'] = $executionTime;

        return $result;
    }

    private function handleItemSearch(array $args): array
    {
        if (empty($args)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatUsage('物品搜索', '/物品 <关键词>')
            ];
        }

        $keyword = implode(' ', $args);
        return [
            'type' => 'text',
            'content' => $this->formatter->formatItemList([], $keyword)
        ];
    }

    private function handlePriceQuery(array $args): array
    {
        if (empty($args)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatUsage('价格查询', '/价格 <物品名> [天数]')
            ];
        }

        $itemName = '';
        $days = 7;

        foreach ($args as $arg) {
            if (is_numeric($arg)) {
                $days = intval($arg);
            } else {
                $itemName .= $arg . ' ';
            }
        }

        $itemName = trim($itemName);

        if (empty($itemName)) {
            return [
                'type' => 'text',
                'content' => $this->formatter->formatError('请提供物品名称')
            ];
        }

        return [
            'type' => 'text',
            'content' => $this->formatter->formatPriceHistory([], $itemName)
        ];
    }

    private function handleRanking(array $args): array
    {
        $rankingType = 'price_desc';

        if (!empty($args)) {
            $typeMap = [
                '最贵' => 'price_desc',
                '最便宜' => 'price_asc',
                '涨幅' => 'change_desc',
                '跌幅' => 'change_asc'
            ];

            $argStr = implode(' ', $args);
            foreach ($typeMap as $key => $type) {
                if (strpos($argStr, $key) !== false) {
                    $rankingType = $type;
                    break;
                }
            }
        }

        return [
            'type' => 'text',
            'content' => $this->formatter->formatRanking([], $rankingType)
        ];
    }

    private function handleDailyPassword(): array
    {
        $passwordData = [
            '农场' => 'ABC123',
            '工厂' => 'DEF456',
            '研究所' => 'GHI789'
        ];

        return [
            'type' => 'text',
            'content' => $this->formatter->formatDailyPassword($passwordData)
        ];
    }

    private function handleSpecialOperation(array $args): array
    {
        try {
            // 构建请求参数
            $params = [
                'page' => 1,
                'page_size' => 5, // QQ消息限制，只显示前5个最优操作
                'sort_by' => 'profit_per_hour',
                'sort_order' => 'desc'
            ];

            // 如果有位置参数，添加位置筛选
            if (!empty($args)) {
                $location = implode(' ', $args);
                // 映射常见的位置别名
                $locationMap = [
                    '工作台' => '工作台',
                    '技术中心' => '技术中心',
                    '制药台' => '制药台',
                    '防具' => '防具工作台'
                ];

                foreach ($locationMap as $alias => $realLocation) {
                    if (strpos($location, $alias) !== false) {
                        $params['location'] = $realLocation;
                        break;
                    }
                }
            }

            // 调用特勤处API接口
            $data = $this->callSpecialOperationAPI($params);

            if (isset($data['error']) && $data['error']) {
                return [
                    'type' => 'text',
                    'content' => "🔧 特勤处查询\n\n❌ " . ($data['error_message'] ?? '获取数据失败，请稍后重试')
                ];
            }

            if (!empty($data['list'])) {
                return [
                    'type' => 'text',
                    'content' => $this->formatter->formatSpecialOperation($data['list'])
                ];
            } else {
                return [
                    'type' => 'text',
                    'content' => $this->formatter->formatSpecialOperation([])
                ];
            }

        } catch (\Exception $e) {
            \think\facade\Log::error('特勤处查询失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());

            return [
                'type' => 'text',
                'content' => "🔧 特勤处查询\n\n❌ 获取数据失败，请稍后重试\n\n错误信息: " . $e->getMessage()
            ];
        }
    }

    /**
     * 调用特勤处API接口
     */
    private function callSpecialOperationAPI(array $params): array
    {
        try {
            // 直接调用数据库查询，避免控制器依赖问题
            return $this->getSpecialOperationDataDirect($params);

        } catch (\Exception $e) {
            \think\facade\Log::error('调用特勤处API失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());

            return [
                'list' => [],
                'total' => 0,
                'error' => true,
                'error_message' => '调用API失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 直接获取特勤处数据
     */
    private function getSpecialOperationDataDirect(array $params): array
    {
        try {
            \think\facade\Log::info('QQ机器人开始获取特勤处数据，参数: ' . json_encode($params));

            // 验证和处理参数
            $validatedParams = $this->validateSpecialOperationParams($params);
            \think\facade\Log::info('参数验证完成: ' . json_encode($validatedParams));

            // 先测试数据库连接
            $testQuery = \think\facade\Db::name('sjz_special_operations')->count();
            \think\facade\Log::info('数据库连接测试成功，总记录数: ' . $testQuery);

            // 获取数据
            $data = $this->fetchSpecialOperationData($validatedParams);
            \think\facade\Log::info('数据获取完成，记录数: ' . count($data['list'] ?? []));

            return $data;

        } catch (\Throwable $e) {
            \think\facade\Log::error('QQ机器人获取特勤处数据失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());
            return [
                'list' => [],
                'total' => 0,
                'page' => $params['page'] ?? 1,
                'page_size' => $params['page_size'] ?? 5,
                'error' => true,
                'error_message' => '获取特勤处数据失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证特勤处查询参数
     */
    private function validateSpecialOperationParams(array $params): array
    {
        return [
            'page' => max(1, intval($params['page'] ?? 1)),
            'page_size' => min(max(1, intval($params['page_size'] ?? 5)), 20),
            'location' => $params['location'] ?? '',
            'min_profit' => isset($params['min_profit']) ? floatval($params['min_profit']) : null,
            'max_cycle' => isset($params['max_cycle']) ? floatval($params['max_cycle']) : null,
            'sort_by' => $params['sort_by'] ?? 'profit_per_hour',
            'sort_order' => $params['sort_order'] ?? 'desc'
        ];
    }

    /**
     * 获取特勤处数据
     */
    private function fetchSpecialOperationData(array $params): array
    {
        // 构建查询
        $query = $this->buildSpecialOperationQuery($params);

        // 获取总记录数
        $total = (clone $query)->count();

        // 如果没有数据，直接返回空列表
        if ($total === 0) {
            return [
                'list' => [],
                'total' => 0,
                'page' => $params['page'],
                'page_size' => $params['page_size'],
                'from_cache' => false
            ];
        }

        // 添加排序
        $this->applySpecialOperationSorting($query, $params);

        // 添加分页并执行查询
        $list = $query->page($params['page'], $params['page_size'])->select()->toArray();

        // 处理数据
        $processedList = $this->processSpecialOperationData($list);

        // 构建返回数据
        return [
            'list' => $processedList,
            'total' => $total,
            'page' => $params['page'],
            'page_size' => $params['page_size'],
            'from_cache' => false
        ];
    }

    /**
     * 构建特勤处查询
     */
    private function buildSpecialOperationQuery(array $params): \think\db\Query
    {
        try {
            $query = \think\facade\Db::name('sjz_special_operations')
                ->where('delete_time', null)
                ->where('status', 1); // 只查询有效数据

            // 应用过滤条件
            $this->applySpecialOperationFilters($query, $params);

            // 准备查询字段
            $fields = [
                'id',
                'object_id',
                'name',
                'image',
                'grade',
                'place',
                'sale_price',
                'cost_price',
                'fee',
                'bail',
                'period',
                'per_count',
                'materials',
                'primary_class',
                'second_class_cn',
                '(sale_price - cost_price - fee) as lirun',
                'IFNULL(CASE WHEN per_count > 0 THEN (sale_price - cost_price - fee) / per_count ELSE 0 END, 0) as profit_per_item',
                'IFNULL(CASE WHEN period > 0 THEN (sale_price - cost_price - fee) / period ELSE 0 END, 0) as profit_per_hour'
            ];

            // 设置查询字段
            $query->field($fields);

            return $query;
        } catch (\Throwable $e) {
            \think\facade\Log::error('构建查询失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }

    /**
     * 应用特勤处过滤条件
     */
    private function applySpecialOperationFilters(\think\db\Query $query, array $params): void
    {
        // 按位置筛选
        if (!empty($params['location'])) {
            $placeValue = $this->getPlaceValue($params['location']);
            if ($placeValue) {
                $query->where('place', $placeValue);
            }
        }

        // 按最小利润筛选
        if (isset($params['min_profit']) && $params['min_profit'] > 0) {
            $query->whereRaw('(sale_price - cost_price - fee) >= ?', [$params['min_profit']]);
        }

        // 按最大周期筛选
        if (isset($params['max_cycle']) && $params['max_cycle'] > 0) {
            $query->where('period', '<=', $params['max_cycle']);
        }
    }

    /**
     * 获取位置值
     */
    private function getPlaceValue(string $locationName): ?string
    {
        $placeMap = [
            '工作台' => 'workbench',
            '技术中心' => 'tech',
            '制药台' => 'pharmacy',
            '防具工作台' => 'armory',
            '防具' => 'armory'
        ];

        return $placeMap[$locationName] ?? null;
    }

    /**
     * 应用特勤处排序
     */
    private function applySpecialOperationSorting(\think\db\Query $query, array $params): void
    {
        $sortField = $params['sort_by'] ?? 'profit_per_hour';
        $direction = strtoupper($params['sort_order'] ?? 'desc') === 'ASC' ? 'asc' : 'desc';

        // 字段映射
        $fieldMap = [
            'name' => 'name',
            'grade' => 'grade',
            'place' => 'place',
            'sale_price' => 'sale_price',
            'cost_price' => 'cost_price',
            'period' => 'period',
            'per_count' => 'per_count',
            'fee' => 'fee',
            'bail' => 'bail'
        ];

        // 特殊处理计算字段的排序
        if ($sortField === 'lirun' || $sortField === 'net_profit') {
            // 净利润排序
            $query->orderRaw('(sale_price - cost_price - fee) ' . $direction);
        } else if ($sortField === 'profit_per_item') {
            // 单个收益排序
            $query->orderRaw('((sale_price - cost_price - fee) / per_count) ' . $direction);
        } else if ($sortField === 'profit_per_hour') {
            // 每小时收益排序
            $query->orderRaw('((sale_price - cost_price - fee) / period) ' . $direction);
        } else {
            // 其他字段排序
            $dbField = $fieldMap[$sortField] ?? $sortField;
            $query->order($dbField, $direction);
        }
    }

    /**
     * 处理特勤处数据
     */
    private function processSpecialOperationData(array $list): array
    {
        $result = [];

        foreach ($list as $item) {
            try {
                if (!isset($item['id'])) {
                    continue;
                }

                // 处理位置显示名称
                $locationDisplayName = $this->getLocationDisplayName($item['place'] ?? '');

                $processed = [
                    'id' => (int)$item['id'],
                    'object_id' => $item['object_id'] ?? '',
                    'name' => $item['name'] ?? '未知物品',
                    'image' => $item['image'] ?? '',
                    'grade' => (int)($item['grade'] ?? 0),
                    'location' => $locationDisplayName,
                    'place' => $item['place'] ?? '',
                    'sale_price' => isset($item['sale_price']) ? (float)$item['sale_price'] : 0,
                    'cost_price' => isset($item['cost_price']) ? (float)$item['cost_price'] : 0,
                    'fee' => isset($item['fee']) ? (float)$item['fee'] : 0,
                    'bail' => isset($item['bail']) ? (float)$item['bail'] : 0,
                    'period' => isset($item['period']) ? (float)$item['period'] : 0,
                    'per_count' => isset($item['per_count']) ? (int)$item['per_count'] : 0,
                    'materials' => $item['materials'] ?? '',
                    'primary_class' => $item['primary_class'] ?? '',
                    'second_class_cn' => $item['second_class_cn'] ?? '',
                    'lirun' => (float)($item['lirun'] ?? 0),
                    'profit_per_item' => (float)($item['profit_per_item'] ?? 0),
                    'profit_per_hour' => (float)($item['profit_per_hour'] ?? 0),

                    // 兼容旧字段名
                    'current_price' => isset($item['sale_price']) ? (float)$item['sale_price'] : 0,
                    'cost' => isset($item['cost_price']) ? (float)$item['cost_price'] : 0,
                    'net_profit' => (float)($item['lirun'] ?? 0),
                    'production_cycle' => (float)($item['period'] ?? 0),
                    'production_quantity' => (int)($item['per_count'] ?? 0),
                    'deposit' => isset($item['bail']) ? (float)$item['bail'] : 0
                ];

                $result[] = $processed;

            } catch (\Throwable $e) {
                \think\facade\Log::error('处理数据项失败: ' . $e->getMessage() . "\n数据: " . json_encode($item, JSON_UNESCAPED_UNICODE));
            }
        }

        return $result;
    }

    /**
     * 获取位置显示名称
     */
    private function getLocationDisplayName(string $place): string
    {
        $locationMap = [
            'workbench' => '工作台',
            'tech' => '技术中心',
            'pharmacy' => '制药台',
            'armory' => '防具工作台'
        ];

        return $locationMap[$place] ?? $place;
    }

    private function handleHelp(): array
    {
        return [
            'type' => 'text',
            'content' => $this->formatter->formatHelp($this->supportedCommands)
        ];
    }

    private function handleUnknownCommand(array $command): array
    {
        return [
            'type' => 'text',
            'content' => $this->formatter->formatUnknownCommand($command['raw_command'] ?? '')
        ];
    }

    private function initSupportedCommands(): void
    {
        $this->supportedCommands = [
            'item_search' => [
                'name' => '物品搜索',
                'aliases' => ['物品', '搜索', 'item', 'search'],
                'usage' => '/物品 <关键词>',
                'description' => '搜索游戏物品信息'
            ],
            'price_query' => [
                'name' => '价格查询',
                'aliases' => ['价格', 'price'],
                'usage' => '/价格 <物品名> [天数]',
                'description' => '查询物品价格历史'
            ],
            'ranking' => [
                'name' => '排行榜',
                'aliases' => ['排行榜', '排行', 'ranking'],
                'usage' => '/排行榜 [类型]',
                'description' => '查看价格排行榜'
            ],
            'daily_password' => [
                'name' => '今日密码',
                'aliases' => ['今日密码', '地图密码', '密码'],
                'usage' => '/今日密码',
                'description' => '获取地图通行密码'
            ],
            'special_operation' => [
                'name' => '特勤处',
                'aliases' => ['特勤处', '特勤'],
                'usage' => '/特勤处 [位置]',
                'description' => '查看特勤处操作信息'
            ],
            'help' => [
                'name' => '帮助',
                'aliases' => ['帮助', 'help'],
                'usage' => '/帮助',
                'description' => '显示使用帮助'
            ]
        ];
    }

    public function getSupportedCommands(): array
    {
        return $this->supportedCommands;
    }
}