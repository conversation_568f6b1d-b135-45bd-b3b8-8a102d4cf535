"""
日志系统工具
"""

import os
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger


class LoggerManager:
    """日志管理器"""

    def __init__(self):
        self.initialized = False
        self.loggers: Dict[str, Any] = {}
        # 默认日志级别
        self.log_level = "INFO"
    
    def setup_logger(self, name: Optional[str] = None) -> None:
        """设置日志系统"""
        if self.initialized:
            return
        
        # 移除默认处理器
        logger.remove()
        
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # 获取日志级别
        log_level = self.log_level
        log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

        # 控制台输出
        logger.add(
            sys.stdout,
            format=log_format,
            level=log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )

        # 文件输出
        # 主日志文件
        logger.add(
            log_dir / "qqbot.log",
            format=log_format,
            level=log_level,
            rotation="1 day",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        # 错误日志文件
        logger.add(
            log_dir / "error.log",
            format=log_format,
            level="ERROR",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        # 调试日志文件（仅在DEBUG级别时）
        if log_level == "DEBUG":
            logger.add(
                log_dir / "debug.log",
                format=log_format,
                level="DEBUG",
                rotation="100 MB",
                retention="7 days",
                compression="zip",
                backtrace=True,
                diagnose=True,
                encoding="utf-8"
            )
        
        self.initialized = True
        logger.info("日志系统初始化完成")
    
    def get_logger(self, name: str) -> Any:
        """获取指定名称的日志器"""
        if not self.initialized:
            self.setup_logger()
        
        if name not in self.loggers:
            # 创建带名称的日志器
            self.loggers[name] = logger.bind(name=name)
        
        return self.loggers[name]
    
    def set_level(self, level: str) -> None:
        """设置日志级别"""
        self.log_level = level
        # 重新初始化日志系统
        self.initialized = False
        self.setup_logger()
        logger.info(f"日志级别已设置为: {level}")

    def add_file_handler(self, file_path: str, level: str = "INFO", **kwargs) -> None:
        """添加文件处理器"""
        log_format = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
        logger.add(
            file_path,
            format=log_format,
            level=level,
            rotation=kwargs.get('rotation', "1 day"),
            retention=kwargs.get('retention', "30 days"),
            compression=kwargs.get('compression', "zip"),
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        logger.info(f"已添加文件处理器: {file_path}")
    
    def remove_handler(self, handler_id: int) -> None:
        """移除处理器"""
        logger.remove(handler_id)
    
    def get_log_files(self) -> list:
        """获取日志文件列表"""
        log_dir = Path("logs")
        if not log_dir.exists():
            return []
        
        log_files = []
        for file_path in log_dir.glob("*.log*"):
            if file_path.is_file():
                stat = file_path.stat()
                log_files.append({
                    'name': file_path.name,
                    'path': str(file_path),
                    'size': stat.st_size,
                    'modified': stat.st_mtime
                })
        
        return sorted(log_files, key=lambda x: x['modified'], reverse=True)
    
    def clean_old_logs(self, days: int = 30) -> int:
        """清理旧日志文件"""
        import time

        log_dir = Path("logs")
        if not log_dir.exists():
            return 0
        
        cutoff_time = time.time() - (days * 24 * 60 * 60)
        cleaned_count = 0
        
        for file_path in log_dir.glob("*.log*"):
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    cleaned_count += 1
                    logger.info(f"已删除旧日志文件: {file_path.name}")
                except Exception as e:
                    logger.error(f"删除日志文件失败: {file_path.name}, 错误: {e}")
        
        return cleaned_count
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        log_dir = Path("logs")
        if not log_dir.exists():
            return {
                'total_files': 0,
                'total_size': 0,
                'files': []
            }
        
        files = self.get_log_files()
        total_size = sum(f['size'] for f in files)
        
        return {
            'total_files': len(files),
            'total_size': total_size,
            'total_size_mb': round(total_size / 1024 / 1024, 2),
            'log_dir': str(log_dir),
            'files': files
        }


# 全局日志管理器实例
_logger_manager = LoggerManager()


def setup_logger(name: Optional[str] = None) -> None:
    """设置日志系统"""
    _logger_manager.setup_logger(name)


def get_logger(name: str = "qqbot") -> Any:
    """获取日志器"""
    return _logger_manager.get_logger(name)


def set_log_level(level: str) -> None:
    """设置日志级别"""
    _logger_manager.set_level(level)


def add_file_handler(file_path: str, level: str = "INFO", **kwargs) -> None:
    """添加文件处理器"""
    _logger_manager.add_file_handler(file_path, level, **kwargs)


def get_log_files() -> list:
    """获取日志文件列表"""
    return _logger_manager.get_log_files()


def clean_old_logs(days: int = 30) -> int:
    """清理旧日志文件"""
    return _logger_manager.clean_old_logs(days)


def get_log_stats() -> Dict[str, Any]:
    """获取日志统计信息"""
    return _logger_manager.get_log_stats()


# 便捷的日志函数
def log_debug(message: str, **kwargs) -> None:
    """记录调试日志"""
    get_logger().debug(message, **kwargs)


def log_info(message: str, **kwargs) -> None:
    """记录信息日志"""
    get_logger().info(message, **kwargs)


def log_warning(message: str, **kwargs) -> None:
    """记录警告日志"""
    get_logger().warning(message, **kwargs)


def log_error(message: str, **kwargs) -> None:
    """记录错误日志"""
    get_logger().error(message, **kwargs)


def log_critical(message: str, **kwargs) -> None:
    """记录严重错误日志"""
    get_logger().critical(message, **kwargs)


def log_exception(message: str, **kwargs) -> None:
    """记录异常日志"""
    get_logger().exception(message, **kwargs)


# 装饰器
def log_function_call(func):
    """记录函数调用的装饰器"""
    def wrapper(*args, **kwargs):
        logger = get_logger()
        logger.debug(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


def log_async_function_call(func):
    """记录异步函数调用的装饰器"""
    async def wrapper(*args, **kwargs):
        logger = get_logger()
        logger.debug(f"调用异步函数: {func.__name__}")
        try:
            result = await func(*args, **kwargs)
            logger.debug(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"异步函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper


# 初始化日志系统
setup_logger()
