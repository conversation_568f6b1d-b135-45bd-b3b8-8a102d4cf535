<?php

namespace app\qqbot\service;

/**
 * 消息格式化器
 * 负责将业务数据格式化为适合QQ发送的消息格式
 */
class MessageFormatter
{
    /**
     * 格式化物品列表
     */
    public function formatItemList(array $data, string $keyword): string
    {
        if (empty($data['items'])) {
            return "🔍 搜索结果：{$keyword}\n\n❌ 未找到相关物品\n\n💡 建议：\n• 检查关键词拼写\n• 尝试使用更简短的关键词\n• 使用物品的部分名称搜索";
        }

        $message = "🔍 搜索结果：{$keyword}\n\n";
        $items = array_slice($data['items'], 0, 5); // 限制显示5个结果

        foreach ($items as $index => $item) {
            $num = $index + 1;
            $name = $item['object_name'] ?? '未知物品';
            $price = isset($item['price_change']['price']) ? number_format($item['price_change']['price'], 0) : '暂无';
            $grade = $this->getGradeName($item['grade'] ?? 0);
            $change = $item['price_24h_ago'] ?? 0;
            $changeIcon = $change > 0 ? '↗️' : ($change < 0 ? '↘️' : '➡️');
            $changeColor = $change > 0 ? '🟢' : ($change < 0 ? '🔴' : '⚪');

            $message .= "📦 {$num}. {$name}\n";
            $message .= "💰 价格：{$price}金币\n";
            $message .= "⭐ 等级：{$grade}\n";
            
            if ($change != 0) {
                $changePercent = $price > 0 ? round(($change / ($price - $change)) * 100, 1) : 0;
                $message .= "📊 24h：{$changeIcon}{$change} ({$changePercent}%)\n";
            }
            
            $message .= "\n";
        }

        if ($data['total'] > 5) {
            $remaining = $data['total'] - 5;
            $message .= "💡 还有{$remaining}个结果，请使用更精确的关键词";
        }

        return $message;
    }

    /**
     * 格式化价格历史
     */
    public function formatPriceHistory(array $data, string $itemName): string
    {
        if (empty($data['history'])) {
            return "📈 {$itemName} 价格查询\n\n❌ 暂无价格数据\n\n💡 可能原因：\n• 物品名称不正确\n• 该物品暂未有交易记录";
        }

        $stats = $data['statistics'] ?? [];
        $currentPrice = $stats['end_price'] ?? 0;
        $startPrice = $stats['start_price'] ?? 0;
        $change = $currentPrice - $startPrice;
        $changePercent = $startPrice > 0 ? round(($change / $startPrice) * 100, 1) : 0;
        $changeIcon = $change > 0 ? '↗️' : ($change < 0 ? '↘️' : '➡️');

        $message = "📈 {$itemName} 价格走势\n\n";
        $message .= "💰 当前价格：" . number_format($currentPrice, 0) . "金币\n";
        $message .= "📊 价格变化：{$changeIcon}" . number_format($change, 0) . " ({$changePercent}%)\n";
        
        if (isset($stats['min_price'])) {
            $message .= "📉 最低价：" . number_format($stats['min_price'], 0) . "金币\n";
        }
        
        if (isset($stats['max_price'])) {
            $message .= "📈 最高价：" . number_format($stats['max_price'], 0) . "金币\n";
        }
        
        if (isset($stats['avg_price'])) {
            $message .= "📊 平均价：" . number_format($stats['avg_price'], 0) . "金币\n";
        }

        $message .= "\n🔄 数据更新：" . date('m-d H:i');
        $message .= "\n💡 输入 \"/价格 {$itemName} 30\" 查看更长周期";

        return $message;
    }

    /**
     * 格式化排行榜
     */
    public function formatRanking(array $data, string $type): string
    {
        if (empty($data['list'])) {
            return "🏆 排行榜查询\n\n❌ 暂无排行数据\n\n💡 请稍后重试或联系管理员";
        }

        $typeNames = [
            'price_desc' => '最贵物品',
            'price_asc' => '最便宜物品', 
            'change_desc' => '涨幅最大',
            'change_asc' => '跌幅最大'
        ];

        $typeName = $typeNames[$type] ?? '价格排行';
        $message = "🏆 {$typeName} TOP10\n\n";

        $icons = ['👑', '🥈', '🥉'];
        
        foreach (array_slice($data['list'], 0, 10) as $index => $item) {
            $rank = $index + 1;
            $icon = $icons[$index] ?? "🔸";
            $name = $item['object_name'] ?? '未知物品';
            $price = isset($item['price']) ? number_format($item['price'], 0) : '0';

            $message .= "{$icon} {$rank}. {$name}\n";
            $message .= "💰 {$price}金币\n\n";
        }

        $message .= "🔄 更新时间：" . date('m-d H:i');
        $message .= "\n💡 输入其他排行类型：最便宜、涨幅、跌幅";

        return $message;
    }

    /**
     * 格式化今日密码
     */
    public function formatDailyPassword(array $passwords): string
    {
        if (empty($passwords)) {
            return "🗝️ 今日密码查询\n\n❌ 暂无密码数据\n\n💡 请联系管理员更新密码信息";
        }

        $message = "🗝️ 今日地图密码 (" . date('Y-m-d') . ")\n\n";

        $icons = [
            '农场' => '🏭',
            '工厂' => '🏢', 
            '研究所' => '🔬',
            '军事基地' => '🏰',
            '港口' => '🚢',
            '机场' => '✈️'
        ];

        foreach ($passwords as $location => $password) {
            $icon = $icons[$location] ?? '📍';
            $message .= "{$icon} {$location}：{$password}\n";
        }

        $message .= "\n⏰ 密码重置：每日 00:00 UTC+8";
        $message .= "\n💡 复制时请注意大小写";

        return $message;
    }

    /**
     * 格式化特勤处信息
     */
    public function formatSpecialOperation(array $data): string
    {
        if (empty($data)) {
            return "🔧 特勤处查询\n\n❌ 暂无特勤处数据\n\n💡 请稍后重试或联系管理员";
        }

        $message = "🔧 特勤处操作建议\n\n";
        $message .= "💎 推荐操作（按利润排序）：\n\n";

        foreach ($data as $index => $item) {
            $num = $index + 1;
            $name = $item['name'] ?? '未知物品';
            $place = $item['location'] ?? $item['place'] ?? '未知位置';
            $netProfit = $item['net_profit'] ?? $item['lirun'] ?? 0;
            $profitPerHour = $item['profit_per_hour'] ?? 0;
            $period = $item['period'] ?? $item['production_cycle'] ?? 0;
            $grade = $item['grade'] ?? 0;

            // 根据品质显示不同的图标
            $gradeIcon = match($grade) {
                1 => '⚪', // 白色
                2 => '🟢', // 绿色
                3 => '🔵', // 蓝色
                4 => '🟣', // 紫色
                5 => '🟠', // 橙色
                default => '⚪'
            };

            $message .= "{$num}. {$gradeIcon} {$name}\n";
            $message .= "📍 位置：{$place}\n";
            $message .= "💰 净利润：" . number_format($netProfit, 0) . "金币\n";
            $message .= "⏱️ 每小时：" . number_format($profitPerHour, 0) . "金币\n";
            $message .= "🕐 周期：{$period}小时\n\n";
        }

        $message .= "🔄 数据更新：" . date('m-d H:i');
        $message .= "\n💡 输入 \"/特勤处 工作台\" 查看具体位置";

        return $message;
    }

    /**
     * 格式化帮助信息
     */
    public function formatHelp(array $commands): string
    {
        $message = "🤖 三角洲助手使用指南\n\n";

        $categories = [
            '📦 物品查询' => ['item_search', 'price_query'],
            '📊 数据查询' => ['ranking', 'special_operation'],
            '🗝️ 实用工具' => ['daily_password', 'help']
        ];

        foreach ($categories as $category => $commandKeys) {
            $message .= "{$category}\n";
            
            foreach ($commandKeys as $key) {
                if (isset($commands[$key])) {
                    $cmd = $commands[$key];
                    $aliases = implode('、', array_slice($cmd['aliases'], 0, 2));
                    $message .= "• {$aliases} - {$cmd['description']}\n";
                }
            }
            
            $message .= "\n";
        }

        $message .= "💡 提示：支持模糊搜索，如 \"/物品 AK\"\n";
        $message .= "🌐 官网：https://www.dfhub.cn\n";
        $message .= "💬 QQ群：292182511";

        return $message;
    }

    /**
     * 格式化使用说明
     */
    public function formatUsage(string $commandName, string $usage): string
    {
        return "❓ {$commandName}使用方法：\n\n{$usage}\n\n💡 示例：{$usage}";
    }

    /**
     * 格式化错误信息
     */
    public function formatError(string $error): string
    {
        return "❌ {$error}\n\n💡 输入 \"/帮助\" 查看使用说明";
    }

    /**
     * 格式化未知命令
     */
    public function formatUnknownCommand(string $command): string
    {
        $message = "❓ 未知指令：{$command}\n\n";
        $message .= "💡 支持的指令：\n";
        $message .= "• /物品 - 搜索物品\n";
        $message .= "• /价格 - 查询价格\n";
        $message .= "• /排行榜 - 查看排行\n";
        $message .= "• /今日密码 - 获取密码\n";
        $message .= "• /帮助 - 查看帮助\n";

        return $message;
    }

    /**
     * 获取等级名称
     */
    private function getGradeName(int $grade): string
    {
        $gradeNames = [
            0 => '0级',
            1 => '1级(普通)',
            2 => '2级(优秀)', 
            3 => '3级(稀有)',
            4 => '4级(史诗)',
            5 => '5级(传说)',
            6 => '6级(神话)'
        ];

        return $gradeNames[$grade] ?? "{$grade}级";
    }
}
