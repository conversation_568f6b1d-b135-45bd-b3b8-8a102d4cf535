<?php
declare(strict_types=1);

namespace app\api\controller;

use think\facade\Db;
use think\Request;
use think\facade\Log;
use app\common\controller\Frontend;
use app\api\service\CacheManager;
use app\api\service\ResponseAdapter;
use think\App;

/**
 * 特勤处相关API控制器
 * 
 * @api {get} /api/special_operation/list 获取特勤处操作列表
 * @apiVersion 1.0.0
 * @apiName getList
 * @apiGroup SpecialOperation
 * 
 * @apiParam {Number} [page=1] 页码
 * @apiParam {Number} [page_size=20] 每页数量
 * @apiParam {String} [location] 位置筛选（工作台/技术中心/制药台/防具）
 * @apiParam {Number} [min_profit] 最小利润筛选
 * @apiParam {Number} [max_cycle] 最大周期筛选
 * @apiParam {String} [sort_by=profit_per_hour] 排序字段（current_price/cost/production_cycle/production_quantity/net_profit/profit_per_item/profit_per_hour）
 * @apiParam {String} [sort_order=desc] 排序方向（asc/desc）
 * 
 * @apiSuccess {Number} code 状态码（1成功，0失败）
 * @apiSuccess {String} msg 提示信息
 * @apiSuccess {Number} time 时间戳
 * @apiSuccess {Object} data 返回数据
 * @apiSuccess {Array} data.list 列表数据
 * @apiSuccess {Number} data.total 总记录数
 * @apiSuccess {Number} data.page 当前页码
 * @apiSuccess {Number} data.page_size 每页数量
 * @apiSuccess {Boolean} data.from_cache 是否来自缓存
 */
class SpecialOperation extends Frontend
{
    // 不需要登录的方法
    protected array $noNeedLogin = ['getList', 'getDetail', 'getMaterialInfo', 'getBulkMaterialInfo', 'getSpecialOperationData'];

    // 不需要鉴权的方法
    protected array $noNeedPermission = [];

    private CacheManager $cacheManager;

    public function __construct(App $app)
    {
        parent::__construct($app);
        $this->cacheManager = new CacheManager();
    }

    /**
     * 为QQ机器人提供特勤处数据
     *
     * @param array $params
     * @return array
     */
    public function getSpecialOperationData(array $params): array
    {
        try {
            Log::info('QQ机器人开始获取特勤处数据，参数: ' . json_encode($params));

            // 验证和处理参数
            $validatedParams = $this->validateParams($params);
            Log::info('参数验证完成: ' . json_encode($validatedParams));

            // 先测试数据库连接
            $testQuery = Db::name('sjz_special_operations')->count();
            Log::info('数据库连接测试成功，总记录数: ' . $testQuery);

            // 获取数据
            $data = $this->fetchSpecialOperationListData($validatedParams);
            Log::info('数据获取完成，记录数: ' . count($data['list'] ?? []));

            return $data;

        } catch (\Throwable $e) {
            Log::error('QQ机器人获取特勤处数据失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈: " . $e->getTraceAsString());
            return [
                'list' => [],
                'total' => 0,
                'page' => $params['page'] ?? 1,
                'page_size' => $params['page_size'] ?? 5,
                'error' => true,
                'error_message' => '获取特勤处数据失败: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 验证参数（简化版，用于QQ机器人）
     */
    private function validateParams(array $params): array
    {
        return [
            'page' => max(1, intval($params['page'] ?? 1)),
            'page_size' => min(max(1, intval($params['page_size'] ?? 5)), 20),
            'location' => $params['location'] ?? '',
            'min_profit' => isset($params['min_profit']) ? floatval($params['min_profit']) : null,
            'max_cycle' => isset($params['max_cycle']) ? floatval($params['max_cycle']) : null,
            'sort_by' => $params['sort_by'] ?? 'profit_per_hour',
            'sort_order' => $params['sort_order'] ?? 'desc'
        ];
    }
    
    
    /**
     * 获取特勤处操作列表
     *
     * @param Request $request
     * @return \think\Response
     */
    public function getList(Request $request)
    {
        try {
            // 获取并验证请求参数
            $params = $this->validateAndGetParams($request);
            
            // 生成缓存键
            $cacheKey = "special_operation_list_" . md5(json_encode($params));

            // 使用缓存管理器获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($params) {
                return $this->fetchSpecialOperationListData($params);
            }, $params);

            // 确保响应分页参数与请求一致
            $data['page'] = $params['page'];
            $data['from_cache'] = false; // CacheManager 内部处理缓存标识

            return ResponseAdapter::list($data, '获取成功');
            
        } catch (\Throwable $e) {
            Log::error('获取特勤处数据失败: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::error('获取数据失败：' . $e->getMessage());
        }
    }

    /**
     * 获取特勤处操作列表数据
     */
    private function fetchSpecialOperationListData(array $params): array
    {
        // 构建查询
        $query = $this->buildBaseQuery($params);
        
        // 获取总记录数
        $total = (clone $query)->count();
        
        // 如果没有数据，直接返回空列表
        if ($total === 0) {
            return [
                'list' => [],
                'total' => 0,
                'page' => $params['page'],
                'page_size' => $params['page_size'],
                'from_cache' => false
            ];
        }
        
        // 添加排序
        $this->applySorting($query, $params);
        
        // 添加分页并执行查询
        $list = $query->page($params['page'], $params['page_size'])->select()->toArray();
        
        // 处理数据
        $processedList = $this->processData($list);
        
        // 构建返回数据
        return [
            'list' => $processedList,
            'total' => $total,
            'page' => $params['page'],
            'page_size' => $params['page_size'],
            'from_cache' => false
        ];
    }
    
    /**
     * 获取特勤处操作详情 (通过 GET 查询参数)
     * @param Request $request
     * @return \think\Response
     */
    public function getDetail(Request $request)
    {
        $itemId = null; // 初始化
        try {
            // 从 GET 查询参数获取 id
            $id = $request->param('id'); 
            // 记录传入的原始值和类型
            
            // 验证 ID 参数
            if (!isset($id) || !ctype_digit((string)$id) || (int)$id <= 0) {
                Log::error('获取特勤处详情失败: 无效的 ID 参数', ['raw_id' => $id]);
                return ResponseAdapter::validationError('无效的 ID 参数');
            }
            $itemId = (int)$id; // 转换为整数

            // 生成缓存键
            $cacheKey = "special_operation_detail_" . $itemId;

            // 使用缓存管理器获取数据
            $data = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($itemId) {
                return $this->fetchSpecialOperationDetailData($itemId);
            }, ['id' => $itemId]);

            if (!$data) {
                return ResponseAdapter::notFound('未找到相关数据');
            }

            $data['from_cache'] = false;

            return ResponseAdapter::detail($data, '获取成功');
            
        } catch (\Throwable $e) {
            // 使用 $itemId (如果已设置) 记录错误上下文
            $contextId = $itemId ?? '未知';
            Log::error('获取特勤处详情失败 (ID: ' . $contextId . '): ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::serverError('获取详情时发生内部错误');
        }
    }

    /**
     * 获取特勤处操作详情数据
     */
    private function fetchSpecialOperationDetailData(int $itemId): ?array
    {
        // 查询数据 - 使用 $itemId
        // 构建与列表接口一致的查询方式，确保包含所有计算字段
        $query = $this->buildBaseQuery([]);
        $item = $query->where('id', $itemId)
            ->where('delete_time', null)
            ->where('status', 1)
            ->find();
            
        if (!$item) {
            return null;
        }
        
        // 处理返回数据
        $processedData = $this->processData([$item])[0] ?? null;
        
        if (!$processedData) {
            return null;
        }
        
        return $processedData;
    }
    
    /**
     * 获取物品材料详情
     * 
     * @api {get} /api/special_operation/material_info 获取物品材料详情
     * @apiVersion 1.0.0
     * @apiName getMaterialInfo
     * @apiGroup SpecialOperation
     * 
     * @apiParam {String|Number} id 物品ID
     * 
     * @apiSuccess {Number} code 状态码（1成功，0失败）
     * @apiSuccess {String} msg 提示信息
     * @apiSuccess {Number} time 时间戳
     * @apiSuccess {Object} data 返回数据
     * @apiSuccess {String} data.item_id 物品ID
     * @apiSuccess {String} data.name 物品名称
     * @apiSuccess {String} data.description 物品描述
     * @apiSuccess {String} data.type 物品类型
     * @apiSuccess {String} data.sub_type 物品子类型
     * @apiSuccess {String} data.image 物品图片
     * @apiSuccess {Number} data.grade 物品等级
     * @apiSuccess {Object} data.original_data 原始数据
     * @apiSuccess {Boolean} data.from_cache 是否来自缓存
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getMaterialInfo(Request $request)
    {
        try {
            // 获取物品ID参数
            $id = $request->param('id');
            
            if (empty($id)) {
                return ResponseAdapter::validationError('参数错误：缺少物品ID');
            }

            // 生成缓存键
            $cacheKey = "material_info_" . md5($id);

            // 使用缓存管理器获取数据
            $result = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($id) {
                return $this->fetchMaterialInfoData($id);
            }, ['id' => $id]);

            if (!$result) {
                return ResponseAdapter::notFound('未找到物品信息');
            }

            $result['from_cache'] = false;
            return ResponseAdapter::detail($result, '获取成功');

        } catch (\Exception $e) {
            Log::error('获取物品材料详情失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::error('获取失败：' . $e->getMessage());
        }
    }
    
    /**
     * 获取所有材料列表
     * 
     * @api {get} /api/special_operation/all_materials 获取所有材料列表
     * @apiVersion 1.0.0
     * @apiName getAllMaterials
     * @apiGroup SpecialOperation
     * 
     * @apiSuccess {Number} code 状态码（1成功，0失败）
     * @apiSuccess {String} msg 提示信息
     * @apiSuccess {Number} time 时间戳
     * @apiSuccess {Array} data 返回数据
     * @apiSuccess {String} data.item_id 物品ID
     * @apiSuccess {String} data.name 物品名称
     * @apiSuccess {String} data.description 物品描述
     * @apiSuccess {String} data.type 物品类型
     * @apiSuccess {String} data.sub_type 物品子类型
     * @apiSuccess {String} data.image 物品图片
     * @apiSuccess {Number} data.grade 物品等级
     * @apiSuccess {Boolean} data.from_cache 是否来自缓存
     * 
     * @return \think\Response
     */
    public function getAllMaterials()
    {
        try {
            // 生成缓存键
            $cacheKey = "all_materials_list";

            // 使用缓存管理器获取数据
            $formattedMaterials = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_STATIC_DATA, function() {
                return $this->fetchAllMaterialsData();
            });

            return ResponseAdapter::list($formattedMaterials, '获取材料列表成功');

        } catch (\Exception $e) {
            Log::error('获取材料列表失败: ' . $e->getMessage());
            return ResponseAdapter::error('获取材料列表失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 批量获取物品材料详情
     * 
     * @api {post} /api/specialoperation/getBulkMaterialInfo 批量获取物品材料详情
     * @apiVersion 1.0.0
     * @apiName getBulkMaterialInfo
     * @apiGroup SpecialOperation
     * 
     * @apiParam {Array} ids 物品ID数组 (e.g., ["id1", "id2", 123])
     * 
     * @apiSuccess {Number} code 状态码（1成功，0失败）
     * @apiSuccess {String} msg 提示信息
     * @apiSuccess {Number} time 时间戳
     * @apiSuccess {Object} data 返回数据 (以ID为键的对象/Map)
     * @apiSuccess {Object} data.{id} 每个ID对应的材料信息
     * @apiSuccess {String} data.{id}.item_id 物品ID
     * @apiSuccess {String} data.{id}.name 物品名称
     * @apiSuccess {String} data.{id}.description 物品描述
     * @apiSuccess {String} data.{id}.type 物品类型
     * @apiSuccess {String} data.{id}.sub_type 物品子类型
     * @apiSuccess {String} data.{id}.image 物品图片
     * @apiSuccess {Number} data.{id}.grade 物品等级
     * @apiSuccess {Boolean} data.{id}.from_cache 是否来自缓存
     * 
     * @param Request $request
     * @return \think\Response
     */
    public function getBulkMaterialInfo(Request $request)
    {
        try {
            // 从 POST 请求体获取 ids 数组
            $ids = $request->param('ids'); // 使用 param 获取，兼容 form-data 和 json payload

            // 验证 ids 参数
            if (!is_array($ids) || empty($ids)) {
                return json(['code' => 0, 'msg' => '参数错误：ids 必须是一个非空数组', 'time' => time(), 'data' => null]);
            }
            
            // 清理和验证 ID (移除空值、转换为字符串)
            $validIds = array_filter(array_map('strval', $ids), function($id) {
                return !empty($id);
            });
            
            if (empty($validIds)) {
                return ResponseAdapter::validationError('参数错误：有效的 ID 列表为空');
            }

            // 对 ID 进行排序以确保缓存键一致性
            sort($validIds);

            // 生成缓存键
            $cacheKey = "bulk_material_info_" . md5(implode(',', $validIds));

            // 使用缓存管理器获取数据
            $resultsMap = $this->cacheManager->remember($cacheKey, CacheManager::TYPE_PRICE_DATA, function() use ($validIds) {
                return $this->fetchBulkMaterialInfoData($validIds);
            }, ['ids' => $validIds]);

            return ResponseAdapter::list($resultsMap, '获取成功');

        } catch (\Throwable $e) {
            Log::error('批量获取物品材料详情失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ResponseAdapter::error('批量获取失败：' . $e->getMessage());
        }
    }
    

    
    /**
     * 验证并获取请求参数
     *
     * @param Request $request
     * @return array
     */
    private function validateAndGetParams(Request $request): array
    {
        $params = $request->param();
        
        // 确保页码正确
        $page = isset($params['page']) ? max(1, (int)$params['page']) : 1;
        $pageSize = isset($params['page_size']) ? max(1, (int)$params['page_size']) : 20;
        
        return [
            'page' => $page,
            'page_size' => $pageSize,
            'location' => $params['location'] ?? '',
            'min_profit' => isset($params['min_profit']) ? (float)$params['min_profit'] : null,
            'max_cycle' => isset($params['max_cycle']) ? (float)$params['max_cycle'] : null,
            'sort_by' => $params['sort_by'] ?? 'profit_per_hour',
            'sort_order' => $params['sort_order'] ?? 'desc',
        ];
    }
    
    /**
     * 构建基础查询
     *
     * @param array $params
     * @return \think\db\Query
     */
    private function buildBaseQuery(array $params): \think\db\Query
    {
        try {
            $query = Db::name('sjz_special_operations')
                ->where('delete_time', null)
                ->where('status', 1); // 只查询有效数据
                
            // 检查 lirun 字段是否存在
            $tableFields = Db::name('sjz_special_operations')->getFieldsType();
            
            $hasLirunField = isset($tableFields['lirun']);
            
            // 应用过滤条件
            $this->applyFilters($query, $params);
            
            // 准备查询字段
            $fields = [
                'id',
                'object_id',
                'name',
                'image',
                'grade',
                'place',
                'sale_price',
                'cost_price',
                'fee',
                'bail',
                'period',
                'per_count',
                'materials',
                'primary_class',
                'second_class_cn',
            ];
            
            // 根据 lirun 字段是否存在决定如何处理净利润
            if ($hasLirunField) {
                // 如果存在，直接使用
                $fields[] = 'lirun';
            } else {
                // 如果不存在，使用计算值
                $fields[] = '(sale_price - cost_price - fee) as lirun';
            }
            
            // 添加其他计算字段
            $fields[] = 'IFNULL(
                CASE 
                    WHEN per_count > 0 THEN lirun / per_count
                    ELSE 0 
                END, 0
            ) as profit_per_item';
            
            $fields[] = 'IFNULL(
                CASE 
                    WHEN period > 0 THEN lirun / period
                    ELSE 0 
                END, 0
            ) as profit_per_hour';
            
            // 设置查询字段
            $query->field($fields);
            
            return $query;
        } catch (\Throwable $e) {
            Log::error('构建查询失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
            throw $e;
        }
    }
    
    /**
     * 应用过滤条件
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applyFilters(\think\db\Query $query, array $params): void
    {
        // 按位置筛选
        if (!empty($params['location'])) {
            $placeValue = $this->getPlaceValue($params['location']);
            if ($placeValue) {
                $query->where('place', $placeValue);
            }
        }
        
        // 按最小利润筛选
        if (isset($params['min_profit']) && $params['min_profit'] > 0) {
            $query->where('lirun', '>=', $params['min_profit']);
        }
        
        // 按最大周期筛选
        if (isset($params['max_cycle']) && $params['max_cycle'] > 0) {
            $query->where('period', '<=', $params['max_cycle']);
        }
    }
    
    /**
     * 获取位置值
     *
     * @param string $locationName
     * @return string|null
     */
    private function getPlaceValue(string $locationName): ?string
    {
        $placeMap = [
            '工作台' => 'workbench',
            '技术中心' => 'tech',
            '制药台' => 'pharmacy',
            '防具' => 'armory'
        ];
        
        return $placeMap[$locationName] ?? null;
    }
    
    /**
     * 应用排序
     *
     * @param \think\db\Query $query
     * @param array $params
     * @return void
     */
    private function applySorting(\think\db\Query $query, array $params): void
    {
        // 检查 lirun 字段是否存在
        $tableFields = Db::name('sjz_special_operations')->getFieldsType();
        $hasLirunField = isset($tableFields['lirun']);
        
        $sortField = $params['sort_by'] ?? 'profit_per_hour';
        $direction = strtoupper($params['sort_order'] ?? 'desc') === 'ASC' ? 'asc' : 'desc';
        
        // 字段映射
        $fieldMap = [
            'sale_price' => 'sale_price',      // 售价
            'cost_price' => 'cost_price',      // 成本价
            'period' => 'period',              // 生产周期
            'per_count' => 'per_count',        // 生产数量
            'fee' => 'fee',                    // 手续费
            'bail' => 'bail',                  // 保证金
            'lirun' => 'lirun',                // 净利润
            
            // 兼容旧字段名
            'current_price' => 'sale_price',
            'cost' => 'cost_price',
            'net_profit' => 'lirun',
            'production_cycle' => 'period',
            'production_quantity' => 'per_count',
            'deposit' => 'bail'
        ];
        
        // 特殊处理计算字段的排序
        if ($sortField === 'lirun' || $sortField === 'net_profit') {
            // 净利润排序
            if ($hasLirunField) {
                $query->order('lirun', $direction);
            } else {
                $query->orderRaw('(sale_price - cost_price - fee) ' . $direction);
            }
        } else if ($sortField === 'profit_per_item') {
            // 单个收益排序
            if ($hasLirunField) {
                $query->orderRaw('(lirun / per_count) ' . $direction);
            } else {
                $query->orderRaw('((sale_price - cost_price - fee) / per_count) ' . $direction);
            }
        } else if ($sortField === 'profit_per_hour') {
            // 每小时收益排序
            if ($hasLirunField) {
                $query->orderRaw('(lirun / period) ' . $direction);
            } else {
                $query->orderRaw('((sale_price - cost_price - fee) / period) ' . $direction);
            }
        } else if ($sortField === 'fee') {
            // 手续费排序
            $query->order('fee', $direction);
        } else if ($sortField === 'bail' || $sortField === 'deposit') {
            // 保证金排序 - 兼容旧字段名 deposit
            $query->order('bail', $direction);
        } else {
            // 其他字段排序
            $dbField = $fieldMap[$sortField] ?? $sortField;
            $query->order($dbField, $direction);
        }
        
       
    }
    
    /**
     * 处理数据
     *
     * @param array $list
     * @return array
     */
    private function processData(array $list): array
    {
        $result = [];
        
        
        foreach ($list as $item) {
            try {
                if (!isset($item['id'])) {
                    continue;
                }
                
                // 类型转换和默认值处理
                $processed = [
                    'id' => (int)$item['id'],
                    'object_id' => (int)($item['object_id'] ?? 0), // 确保返回 object_id 字段
                    'sale_price' => isset($item['sale_price']) ? (float)$item['sale_price'] : 0, // 售价
                    'cost_price' => isset($item['cost_price']) ? (float)$item['cost_price'] : 0, // 成本价
                    'fee' => isset($item['fee']) ? (float)$item['fee'] : 0, // 手续费
                    'bail' => isset($item['bail']) ? (float)$item['bail'] : 0, // 保证金
                    'lirun' => (float)($item['lirun'] ?? 0), // 使用 lirun 字段的值作为 净利润
                    'period' => (float)($item['period'] ?? 0), // 生产周期
                    'per_count' => (int)($item['per_count'] ?? 0), // 生产数量
                    'profit_per_item' => (float)($item['profit_per_item'] ?? 0),
                    'profit_per_hour' => (float)($item['profit_per_hour'] ?? 0),
                    'grade' => isset($item['grade']) ? (int)$item['grade'] : 1,
                    'location' => $this->getLocationName($item['place'] ?? ''),
                    'name' => $item['name'] ?? '未知物品',
                    'image' => $item['image'] ?? '',
                    'materials' => $item['materials'] ?? '',
                    'primary_class' => $item['primary_class'] ?? '',
                    'second_class_cn' => $item['second_class_cn'] ?? '',
                    
                    // 手续费和保证金百分比
                    'feee' => isset($item['feee']) ? (float)$item['feee'] : 0, // 手续费百分比
                    'baill' => isset($item['baill']) ? (float)$item['baill'] : 0, // 保证金百分比
                    
                    // 兼容旧字段名 - 只为前端兼容性添加
                    'current_price' => isset($item['sale_price']) ? (float)$item['sale_price'] : 0, 
                    'cost' => isset($item['cost_price']) ? (float)$item['cost_price'] : 0,
                    'net_profit' => (float)($item['lirun'] ?? 0),
                    'production_cycle' => (float)($item['period'] ?? 0),
                    'production_quantity' => (int)($item['per_count'] ?? 0),
                    'deposit' => isset($item['bail']) ? (float)$item['bail'] : 0
                ];
                
                $result[] = $processed;
                
            } catch (\Throwable $e) {
                Log::error('处理数据项失败: ' . $e->getMessage() . "\n数据: " . json_encode($item, JSON_UNESCAPED_UNICODE));
            }
        }
        
        return $result;
    }
    
    /**
     * 获取位置名称
     *
     * @param string $place
     * @return string
     */
    private function getLocationName(string $place): string
    {
        $placeMap = [
            'workbench' => '工作台',
            'tech' => '技术中心',
            'pharmacy' => '制药台',
            'armory' => '防具'
        ];
        
        return $placeMap[$place] ?? '未知位置';
    }

    /**
     * 获取材料信息数据
     *
     * @param string $id
     * @return array|null
     */
    private function fetchMaterialInfoData(string $id): ?array
    {
        // 检查ID格式，如果是数字类型，可能需要转换查询方式
        if (is_numeric($id)) {
            // 查询 ba_sjz_items 表获取物品信息
            $itemInfo = Db::table('ba_sjz_items')
                ->where('object_id', $id)
                ->whereOr('id', $id)
                ->find();
        } else {
            // 查询 ba_sjz_items 表获取物品信息
            $itemInfo = Db::table('ba_sjz_items')
                ->where('object_id', $id)
                ->find();
        }

        if ($itemInfo) {
            // 从 ba_sjz_items 表中获取的数据
            return [
                'item_id' => $itemInfo['object_id'] ?? '',
                'name' => $itemInfo['object_name'] ?? '未知物品',
                'description' => $itemInfo['desc'] ?? '',
                'type' => '', // items 表中没有直接对应的字段
                'sub_type' => '', // items 表中没有直接对应的字段
                'image' => $itemInfo['pic'] ?? '',
                'grade' => $itemInfo['grade'] ?? 1,
                'original_data' => $itemInfo,
                'from_cache' => false
            ];
        }

        // 如果在 ba_sjz_items 中没找到，尝试在 ba_sjz_collectibles 中查找
        if (is_numeric($id)) {
            $collectibleInfo = Db::table('ba_sjz_collectibles')
                ->where('object_id', $id)
                ->whereOr('id', $id)
                ->find();
        } else {
            $collectibleInfo = Db::table('ba_sjz_collectibles')
                ->where('object_id', $id)
                ->find();
        }

        if ($collectibleInfo) {
            // 从 ba_sjz_collectibles 表中获取的数据
            return [
                'item_id' => $collectibleInfo['object_id'] ?? '',
                'name' => $collectibleInfo['name'] ?? '未知物品',
                'description' => '', // collectibles 表中没有描述字段
                'type' => $collectibleInfo['department'] ?? '',
                'sub_type' => $collectibleInfo['category'] ?? '',
                'image' => '', // collectibles 表中没有图片字段
                'grade' => 1, // collectibles 表中没有等级字段
                'original_data' => $collectibleInfo,
                'from_cache' => false
            ];
        }

        // 如果仍然没找到，返回一个通用结果以避免前端显示出错
        return [
            'item_id' => $id,
            'name' => '物品#' . substr($id, -4),
            'description' => '',
            'type' => '',
            'sub_type' => '',
            'image' => '',
            'grade' => 1,
            'original_data' => null,
            'from_cache' => false
        ];
    }

    /**
     * 获取所有材料数据
     *
     * @return array
     */
    private function fetchAllMaterialsData(): array
    {
        // 查询 ba_sjz_items 表获取所有可能的材料物品
        $materials = Db::table('ba_sjz_items')
            ->field('object_id as item_id, object_name as name, desc as description, pic as image, grade')
            ->select()
            ->toArray();

        // 查询 ba_sjz_collectibles 表中的物品
        $collectibles = Db::table('ba_sjz_collectibles')
            ->where('category', '材料')
            ->where('status', 1) // 只获取启用状态的物品
            ->field('object_id as item_id, name, department as type, category as sub_type')
            ->select()
            ->toArray();

        // 合并两个表中的材料数据
        $allMaterials = array_merge($materials, $collectibles);

        // 格式化数据
        $formattedMaterials = [];
        foreach ($allMaterials as $material) {
            $formattedMaterials[] = [
                'item_id' => $material['item_id'] ?? '',
                'name' => $material['name'] ?? '未知材料',
                'description' => $material['description'] ?? '',
                'type' => $material['type'] ?? '',
                'sub_type' => $material['sub_type'] ?? '',
                'image' => $material['image'] ?? '',
                'grade' => $material['grade'] ?? 1,
                'from_cache' => false
            ];
        }

        return $formattedMaterials;
    }

    /**
     * 批量获取材料信息数据
     *
     * @param array $validIds
     * @return array
     */
    private function fetchBulkMaterialInfoData(array $validIds): array
    {
        $itemsInfoMap = [];
        $collectiblesInfoMap = [];

        // 查询 ba_sjz_items 表 - 使用闭包构建 OR 条件
        $itemsDbResult = Db::table('ba_sjz_items')
            ->where(function($query) use ($validIds) {
                $query->whereIn('object_id', $validIds)
                      ->whereOr('id', 'in', $validIds);
            })
            ->select()
            ->toArray();
        foreach ($itemsDbResult as $item) {
             $key = $item['object_id'] ?? $item['id'] ?? null;
             if ($key) {
                 $itemsInfoMap[(string)$key] = $item;
             }
        }

        // 查询 ba_sjz_collectibles 表 - 使用闭包构建 OR 条件
        $collectiblesDbResult = Db::table('ba_sjz_collectibles')
            ->where(function($query) use ($validIds) {
                $query->whereIn('object_id', $validIds)
                      ->whereOr('id', 'in', $validIds);
            })
            ->select()
            ->toArray();
         foreach ($collectiblesDbResult as $item) {
             $key = $item['object_id'] ?? $item['id'] ?? null;
             if ($key) {
                 $collectiblesInfoMap[(string)$key] = $item;
             }
        }

        // --- 合并与格式化 ---
        $resultsMap = [];
        foreach ($validIds as $id) {
            $idStr = (string)$id; // 确保使用字符串键
            $found = false;

            // 优先使用 ba_sjz_items 的数据
            if (isset($itemsInfoMap[$idStr])) {
                $itemInfo = $itemsInfoMap[$idStr];
                $resultsMap[$idStr] = [
                    'item_id' => $itemInfo['object_id'] ?? $idStr,
                    'name' => $itemInfo['object_name'] ?? '未知物品',
                    'description' => $itemInfo['desc'] ?? '',
                    'type' => '',
                    'sub_type' => '',
                    'image' => $itemInfo['pic'] ?? '',
                    'grade' => $itemInfo['grade'] ?? 1,
                    'original_data' => $itemInfo,
                    'from_cache' => false
                ];
                $found = true;
            }

            // 如果在 items 未找到，再查找 collectibles
            if (!$found && isset($collectiblesInfoMap[$idStr])) {
                 $collectibleInfo = $collectiblesInfoMap[$idStr];
                 $resultsMap[$idStr] = [
                    'item_id' => $collectibleInfo['object_id'] ?? $idStr,
                    'name' => $collectibleInfo['name'] ?? '未知物品',
                    'description' => '',
                    'type' => $collectibleInfo['department'] ?? '',
                    'sub_type' => $collectibleInfo['category'] ?? '',
                    'image' => '',
                    'grade' => 1,
                    'original_data' => $collectibleInfo,
                    'from_cache' => false
                ];
                $found = true;
            }

            // 如果两个表都没找到，提供默认值
            if (!$found) {
                $resultsMap[$idStr] = [
                    'item_id' => $idStr,
                    'name' => '物品#' . substr($idStr, -4),
                    'description' => '',
                    'type' => '',
                    'sub_type' => '',
                    'image' => '',
                    'grade' => 1,
                    'original_data' => null,
                    'from_cache' => false
                ];
            }
        }

        return $resultsMap;
    }
}