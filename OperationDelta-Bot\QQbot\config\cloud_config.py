"""
云端API配置管理
"""

import os
import logging
from typing import Dict, Any
from pathlib import Path
import yaml

# 直接使用logging，避免循环导入
logger = logging.getLogger(__name__)


class CloudConfig:
    """云端API配置类"""
    
    def __init__(self):
        self.enabled = False
        self.base_url = ""
        self.auth_token = ""
        self.timeout = 30
        self.max_retries = 3
        
        # 命令检测配置
        self.command_prefixes = ['/', '！', '!']
        
        # NapCat配置
        self.napcat_host = "127.0.0.1"
        self.napcat_ws_port = 3001
        self.napcat_api_port = 3000
        self.napcat_access_token = ""
    
    def load_from_dict(self, config_dict: Dict[str, Any]):
        """从字典加载配置"""
        cloud_api = config_dict.get('cloud_api', {})
        
        self.enabled = cloud_api.get('enabled', False)
        self.base_url = cloud_api.get('base_url', '').rstrip('/')
        self.auth_token = cloud_api.get('auth_token', '')
        self.timeout = cloud_api.get('timeout', 30)
        self.max_retries = cloud_api.get('max_retries', 3)
        
        # 命令检测配置
        command_config = config_dict.get('command_detection', {})
        self.command_prefixes = command_config.get('prefixes', self.command_prefixes)
        
        # NapCat配置
        napcat_config = config_dict.get('napcat', {})
        self.napcat_host = napcat_config.get('host', self.napcat_host)
        self.napcat_ws_port = napcat_config.get('ws_port', self.napcat_ws_port)
        self.napcat_api_port = napcat_config.get('api_port', self.napcat_api_port)
        self.napcat_access_token = napcat_config.get('access_token', self.napcat_access_token)


def load_cloud_config() -> CloudConfig:
    """加载云端API配置"""
    config = CloudConfig()
    
    try:
        # 配置文件路径
        config_file = Path(__file__).parent / 'cloud_config.yml'
        
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_dict = yaml.safe_load(f) or {}
                config.load_from_dict(config_dict)
                logger.info("云端API配置加载成功")
        else:
            logger.warning(f"云端API配置文件不存在: {config_file}")
    
    except Exception as e:
        logger.error(f"加载云端API配置失败: {e}")
    
    # 环境变量覆盖
    if os.getenv('CLOUD_API_ENABLED'):
        config.enabled = os.getenv('CLOUD_API_ENABLED', '').lower() == 'true'
    
    if os.getenv('CLOUD_API_BASE_URL'):
        config.base_url = os.getenv('CLOUD_API_BASE_URL', '').rstrip('/')
    
    if os.getenv('CLOUD_API_TOKEN'):
        config.auth_token = os.getenv('CLOUD_API_TOKEN', '')
    
    return config


def get_cloud_config() -> CloudConfig:
    """获取云端API配置"""
    return load_cloud_config()


def create_default_config_file():
    """创建默认配置文件"""
    config_file = Path(__file__).parent / 'cloud_config.yml'
    
    if config_file.exists():
        logger.info("云端API配置文件已存在，跳过创建")
        return
    
    default_config = """# 云端API配置
cloud_api:
  # 是否启用云端API
  enabled: true
  
  # API基础URL
  base_url: "https://your-domain.com/qqbot/BusinessDoc"
  
  # 认证Token
  auth_token: "your-secure-token-here"
  
  # 请求超时时间（秒）
  timeout: 30
  
  # 最大重试次数
  max_retries: 3

# 命令检测配置
command_detection:
  # 命令前缀，只有以这些前缀开头的消息才会转发到云端API
  prefixes:
    - "/"
    - "！"
    - "!"

# NapCat WebSocket配置
napcat:
  # WebSocket服务器地址
  host: "127.0.0.1"
  
  # WebSocket端口
  ws_port: 3001
  
  # HTTP API端口
  api_port: 3000
  
  # 访问令牌（可选）
  access_token: ""
"""
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(default_config)
        logger.info(f"默认云端API配置文件已创建: {config_file}")
    except Exception as e:
        logger.error(f"创建默认云端API配置文件失败: {e}")


if __name__ == "__main__":
    # 创建默认配置文件
    create_default_config_file()
    
    # 测试配置加载
    config = get_cloud_config()
    print(f"云端API启用: {config.enabled}")
    print(f"基础URL: {config.base_url}")
    print(f"命令前缀: {config.command_prefixes}")
    print(f"NapCat配置:")
    print(f"  - 主机: {config.napcat_host}")
    print(f"  - WebSocket端口: {config.napcat_ws_port}")
    print(f"  - HTTP API端口: {config.napcat_api_port}")
    print(f"  - 访问令牌: {'已设置' if config.napcat_access_token else '未设置'}")
