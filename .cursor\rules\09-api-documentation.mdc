---
globs: OperationDelta-Admin_Api_new/docs/**,**/api/**
description: API 文档规范（接口设计、文档格式、版本管理）
---

### API 文档结构

标准文档格式：
- 接口概述：功能描述和使用场景
- 请求方法：GET、POST、PUT、DELETE
- 请求路径：完整的 URL 路径
- 请求参数：必填/可选、类型、说明、示例
- 响应格式：成功/失败响应示例
- 错误码说明：具体错误情况和处理建议
- 示例代码：curl、JavaScript、PHP 等

### 响应格式规范

基于 [OperationDelta-Admin_Api_new/app/api/service/ResponseAdapter.php](mdc:OperationDelta-Admin_Api_new/app/api/service/ResponseAdapter.php) 的统一格式：

成功响应：
```json
{
  "code": 1,
  "msg": "操作成功",
  "time": 1640995200,
  "data": {
    // 具体数据内容
  }
}
```

失败响应：
```json
{
  "code": 0,
  "msg": "错误信息",
  "time": 1640995200,
  "data": {}
}
```

HTTP 状态码响应：
```json
{
  "code": 400,
  "msg": "参数验证失败",
  "time": 1640995200,
  "data": {
    "errors": ["字段验证错误详情"]
  }
}
```

### 分页响应格式

使用 `ResponseAdapter::paginate()` 的标准格式：
```json
{
  "code": 1,
  "msg": "获取成功",
  "time": 1640995200,
  "data": {
    "list": [
      // 数据项
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5
  }
}
```

### 文档示例模板

#### 接口标题
**功能描述**：获取经济学历史数据

**请求方法**：`GET`

**请求路径**：`/api/economics_history`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| page | integer | 否 | 页码，默认1 | 1 |
| pageSize | integer | 否 | 每页数量，默认20 | 20 |
| keyword | string | 否 | 搜索关键词 | "武器" |
| date_start | string | 否 | 开始日期 | "2024-01-01" |
| date_end | string | 否 | 结束日期 | "2024-12-31" |

**成功响应**：
```json
{
  "code": 1,
  "msg": "获取成功",
  "time": 1640995200,
  "data": {
    "list": [
      {
        "id": 1,
        "item_name": "AK-47",
        "price": 2500,
        "date": "2024-01-15",
        "server": "S4正式服"
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20
  }
}
```

**错误响应**：
```json
{
  "code": 400,
  "msg": "参数验证失败",
  "time": 1640995200,
  "data": {
    "errors": ["page 必须是正整数"]
  }
}
```

**请求示例**：
```bash
curl -X GET "https://api.example.com/api/economics_history?page=1&pageSize=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 文档组织结构

参考 [OperationDelta-Admin_Api_new/docs/api/](mdc:OperationDelta-Admin_Api_new/docs/api/) 目录：

```
docs/api/
├── README.md                 # API 总览和快速开始
├── authentication.md         # 认证和授权说明
├── economics_history.md      # 经济学历史相关接口
├── user_management.md        # 用户管理接口
├── item_management.md        # 物品管理接口
├── error_codes.md           # 错误码对照表
└── changelog.md             # API 变更日志
```

### 版本管理

API 版本控制：
- URL 路径版本：`/api/v1/economics_history`
- Header 版本：`Accept: application/vnd.api+json;version=1`
- 兼容性说明：新版本保持向后兼容
- 废弃通知：提前至少 6 个月通知废弃计划

版本文档：
- 每个版本独立文档目录
- 变更日志详细记录
- 迁移指南和示例
- 重大变更的影响评估

### 接口设计原则

RESTful 设计：
- 资源命名使用名词复数：`/users`、`/items`
- HTTP 方法语义化：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 状态码合理使用：200、201、400、401、403、404、500
- 统一的错误响应格式

幂等性保证：
- GET、PUT、DELETE 操作幂等
- POST 操作避免重复提交
- 使用唯一标识符防重复

### 安全文档

认证方式：
- Bearer Token 认证
- API Key 认证
- OAuth2 授权码模式

权限控制：
- 角色权限矩阵
- 接口权限说明
- 敏感操作二次验证

安全注意事项：
- HTTPS 必须使用
- 敏感参数加密传输
- 请求频率限制
- 输入验证要求

### 测试文档

接口测试：
- Postman Collection 导出
- 自动化测试用例
- 性能测试基准
- 压力测试报告

测试环境：
- 测试服务器地址
- 测试账号和权限
- 测试数据说明
- Mock 服务配置

### 文档生成自动化

自动化工具：
- OpenAPI/Swagger 规范
- 代码注释自动生成
- CI/CD 集成文档更新
- 文档版本同步发布

文档质量检查：
- 链接有效性检查
- 示例代码验证
- 参数完整性验证
- 响应格式一致性

### 用户体验优化

交互式文档：
- 在线 API 测试工具
- 实时代码生成器
- 响应数据预览
- 错误场景模拟

多语言支持：
- 中英文双语文档
- 多语言示例代码
- 本地化错误信息
- 文化适应性考虑

### 维护与更新

文档维护流程：
1. 代码变更时同步更新文档
2. 定期审核文档准确性
3. 用户反馈收集和处理
4. 文档质量评估和改进

更新通知：
- 变更通知邮件
- 开发者社区公告
- 版本变更日志
- 迁移指导文档

### 文档评估指标

质量指标：
- 文档完整性：覆盖所有公开接口
- 准确性：示例代码可运行成功
- 及时性：与代码版本保持同步
- 可用性：用户能快速找到需要的信息

用户反馈：
- 文档满意度调查
- 常见问题统计分析
- 使用难点收集
- 改进建议采纳