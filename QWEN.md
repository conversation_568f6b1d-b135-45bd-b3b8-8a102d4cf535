# QWEN.md

This file provides comprehensive guidance for Qwen Code (tongyi.aliyun.com/code) when working with code in this repository.

## 项目整体概述

这是一个名为《三角洲行动》的多端游戏数据管理平台项目。项目旨在为游戏玩家和开发者提供游戏物品数据查询、价格历史跟踪、排行榜统计等核心功能。它由多个独立但相互关联的子项目组成，共同构成了一个完整的生态系统。

### 核心模块

1.  **OperationDelta-Admin_Api_new**: 后端API服务，基于ThinkPHP 8.1，提供数据接口。
2.  **OperationDelta-Web**: Web前端，基于Vue 3 + TypeScript + Naive UI，提供网页版用户界面。
3.  **OperationDelta-Desktop**: 桌面应用，基于Electron + Vue 3 + Element Plus，提供跨平台的桌面客户端。
4.  **OperationDelta-xcx**: 微信小程序，基于原生小程序开发，提供移动端访问。
5.  **OperationDelta-Bot**: QQ机器人 (Python 3 + WebSocket)，提供自动化交互和通知。
6.  **scripts_new**: 数据处理脚本 (Python 3)，负责数据爬取和后台任务。

### 数据架构

- **主数据库**: MySQL 8.0，存储物品数据、价格历史、用户管理等核心信息。
- **缓存层**: Redis，用于热点数据缓存和排行榜缓存，提高访问性能。
- **数据源**: 游戏解包数据 + 实时爬虫数据。
- **数据表**: 定义在 `database/dfnew.sql` 文件中。

## 关键技术要点

### 后端API (ThinkPHP)

- **框架**: ThinkPHP 8.1
- **控制器**: `app/api/controller/`
- **服务层**: `app/api/controller/service/`
- **模型**: `app/common/model/`
- **缓存**: 使用 `cache()` 助手函数，默认Redis存储。
- **验证器**: `app/common/validate/` 目录下。
- **中间件**: `app/middleware/` 用于认证和限流。
- **数据库前缀**: 表名前缀为 `ba_`。

### Web前端 (Vue 3)

- **框架**: Vue 3 + TypeScript 5.7
- **UI库**: Naive UI (主要) 和 Element Plus (桌面端)
- **构建工具**: Vite 6.0
- **状态管理**: Pinia
- **组件目录**: `src/components/`
- **页面路由**: `src/views/`
- **API调用**: `src/api/` 统一管理。
- **工具函数**: `src/utils/`
- **主题支持**: 明暗主题切换。
- **移动端优化**: 需要特别注意响应式设计和移动端体验。

### 桌面应用 (Electron)

- **框架**: Electron
- **前端技术**: Vue 3 + TypeScript + Element Plus
- **主进程**: `electron/main.js`
- **预加载脚本**: `electron/preload.js`
- **构建**: Yarn

### 微信小程序

- **框架**: 微信小程序原生开发
- **语法**: JavaScript ES5 + 小程序API
- **样式**: WXSS
- **模板**: WXML
- **架构**: MVC模式

### 数据处理脚本 (Python)

- **语言**: Python 3
- **主要服务**: 价格爬虫 (`services/price_crawler.py`) 和特勤处价格更新 (`services/special_ops.py`)。
- **通知**: 集成企业微信机器人通知。

### 缓存策略

- **Redis Key规范**: `delta:物品ID:类型` 格式。
- **TTL设置**: 热点数据30分钟，普通数据2小时。
- **缓存更新**: 数据变更时自动失效相关缓存。
- **分布式锁**: 用于防止缓存击穿和并发问题。

## 重要说明

- **中文响应**: 请永远回复中文。
- **修改范围**: 代码修改应尽可能局限在当前任务范围内，保持代码简洁。
- **图片**: 除了小程序，其他页面图片可用SVG代替。
- **测试**: 你不需要测试代码，由用户自行测试。
- **主题**: 前端开发必须兼容明暗主题。
- **移动端**: 前端开发需优化移动端体验，必要时可创建移动端专用组件。

## 许可证

本项目采用 MIT 许可证。
