APP_DEBUG = true

[APP]
DEFAULT_TIMEZONE = Asia/Shanghai

[LANG]
default_lang = zh-cn

[DATABASE]
TYPE = mysql
HOSTNAME = **************
DATABASE = dfnew
USERNAME = xwx
PASSWORD = Xwx19991110@
HOSTPORT = 3306
PREFIX = ba_
CHARSET = utf8mb4
DEBUG = true

[CACHE]
# 全局缓存开关，控制整个缓存系统是否启用
ENABLE = false

# 价格数据缓存开关，控制价格相关数据的缓存（整点2分钟更新策略）
PRICE_ENABLE = true

# 静态数据缓存开关，控制静态数据的长期缓存（24小时TTL）
STATIC_ENABLE = true

# 动态数据缓存开关，控制动态数据的短期缓存（5分钟TTL）
DYNAMIC_ENABLE = true

# 配置数据缓存开关，控制配置数据的超长缓存（7天TTL）
CONFIG_ENABLE = true

# 缓存操作日志开关，控制是否记录缓存的SET、HIT、DELETE等操作日志
LOG_ENABLE = true

# 缓存日志级别，可选值：debug、info、warning、error
LOG_LEVEL = info

[QQBOT]
# QQBot功能总开关，控制是否启用QQBot相关功能
ENABLED = true

# QQBot认证Token，用于验证来自QQBot的请求
# 建议使用强密码生成器生成32位以上的随机字符串
# 支持配置多个Token，用逗号分隔，方便Token轮换
AUTH_TOKEN = your-secure-token-here,backup-token-here

# QQBot消息最大长度限制（字符数）
# QQ单条消息限制约4500字符，建议设置为4000以下
MESSAGE_MAX_LENGTH = 4000

# QQBot IP白名单，限制只有指定IP可以访问
# 留空表示不限制IP，多个IP用逗号分隔
# 例如：127.0.0.1,*************
IP_WHITELIST = 

# QQBot用户黑名单，屏蔽指定用户的请求
# 多个用户ID用逗号分隔
# 例如：123456789,987654321
USER_BLACKLIST = 

# QQBot响应超时时间（秒）
# 超过此时间的请求将被中断
RESPONSE_TIMEOUT = 30

# QQBot调试模式开关
# 开启后会记录更详细的调试信息
DEBUG_MODE = false
