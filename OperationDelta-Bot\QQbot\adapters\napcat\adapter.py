"""
NapCat 适配器主类
"""

import asyncio
from typing import Dict, Any, Optional, List, Callable
from utils import get_logger
from .client import NapCatClient
from .onebot.models import OneBotEvent, MessageEvent
from .onebot.handlers import MessageProcessor
from config.cloud_config import get_cloud_config


class NapCatAdapter:
    """NapCat 适配器"""
    
    def __init__(self):
        """
        初始化适配器
        """
        self.logger = get_logger(__name__)
        
        # 从配置文件读取NapCat配置
        config = get_cloud_config()
        
        # 创建客户端
        self.client = NapCatClient(
            host=config.napcat_host,
            port=config.napcat_ws_port,
            access_token=config.napcat_access_token if config.napcat_access_token else None
        )
        
        # 消息处理器（保留用于基础消息解析）
        self.message_processor = MessageProcessor()

        # 运行状态
        self.running = False

        # 注册默认事件处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认事件处理器"""
        # 适配器层只负责基础事件转发，不处理具体业务逻辑
        # 业务逻辑处理由应用层负责
        pass
    
    async def start(self):
        """启动适配器"""
        try:
            self.logger.info("正在启动 NapCat 适配器...")
            self.running = True
            
            # 启动客户端
            await self.client.start()
            
        except Exception as e:
            self.logger.error(f"启动适配器失败: {e}")
            self.running = False
            raise
    
    async def stop(self):
        """停止适配器"""
        try:
            self.logger.info("正在停止 NapCat 适配器...")
            self.running = False
            
            # 停止客户端
            await self.client.disconnect()
            
            self.logger.info("NapCat 适配器已停止")
            
        except Exception as e:
            self.logger.error(f"停止适配器时发生错误: {e}")
    
    # 适配器层不再处理具体的业务逻辑
    # 所有业务逻辑处理都移至应用层
    
    # 命令处理和插件管理功能移至应用层
    
    def get_status(self) -> Dict[str, Any]:
        """获取适配器状态"""
        config = get_cloud_config()
        return {
            "running": self.running,
            "client_status": self.client.get_status(),
            "config": {
                "host": config.napcat_host,
                "ws_port": config.napcat_ws_port,
                "api_port": config.napcat_api_port,
                "has_access_token": bool(config.napcat_access_token)
            }
        }
    
    async def send_message(self, message_type: str, target_id: int, message: str) -> bool:
        """发送消息"""
        return await self.client.send_message(message_type, target_id, message)
    
    async def get_login_info(self) -> Optional[Dict[str, Any]]:
        """获取登录信息"""
        return await self.client.get_login_info()
    
    async def get_friend_list(self) -> Optional[List[Dict[str, Any]]]:
        """获取好友列表"""
        return await self.client.get_friend_list()
    
    async def get_group_list(self) -> Optional[List[Dict[str, Any]]]:
        """获取群列表"""
        return await self.client.get_group_list()

    async def get_group_info(self, group_id: int, no_cache: bool = False) -> Optional[Dict[str, Any]]:
        """获取群信息"""
        return await self.client.get_group_info(group_id, no_cache)

    async def get_group_member_list(self, group_id: int) -> Optional[List[Dict[str, Any]]]:
        """获取群成员列表"""
        return await self.client.get_group_member_list(group_id)

    async def get_group_member_info(self, group_id: int, user_id: int, no_cache: bool = False) -> Optional[Dict[str, Any]]:
        """获取群成员信息"""
        return await self.client.get_group_member_info(group_id, user_id, no_cache)


    # 默认命令处理器移至应用层
