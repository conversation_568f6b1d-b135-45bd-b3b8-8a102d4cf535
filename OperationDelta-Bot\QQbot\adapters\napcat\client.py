"""
NapCat WebSocket 客户端
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, Callable, List
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException

from utils import get_logger
from .onebot.models import OneBotEvent
from .onebot.parser import OneBotParser


class NapCatClient:
    """NapCat WebSocket 客户端"""
    
    def __init__(self,
                 host: str = "127.0.0.1",
                 port: int = 3001,
                 access_token: Optional[str] = None):
        """
        初始化 NapCat 客户端

        Args:
            host: NapCat 服务器地址
            port: WebSocket 端口
            access_token: 访问令牌
        """
        self.host = host
        self.port = port
        self.access_token = access_token

        self.logger = get_logger(__name__)
        self.parser = OneBotParser()

        # 连接状态
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
        self.connected = False
        self.running = False

        # 事件处理器
        self.event_handlers: Dict[str, List[Callable]] = {}

        # 默认配置
        self.reconnect_interval = 5  # 重连间隔（秒）
        self.max_reconnect_attempts = 10  # 最大重连次数
        self.reconnect_attempts = 0

        # 心跳配置
        self.heartbeat_interval = 30  # 心跳间隔（秒）
        self.last_heartbeat = 0

        # API调用配置
        self.api_timeout = 30  # API超时时间（秒）

        # API响应处理
        self.pending_api_calls = {}  # 存储待处理的API调用（同步模式）
        self.response_callbacks = {}  # 存储异步响应回调

        # 消息发送队列
        self._send_lock = asyncio.Lock()  # 防止并发发送冲突
    
    @property
    def websocket_url(self) -> str:
        """获取 WebSocket 连接 URL"""
        url = f"ws://{self.host}:{self.port}"
        if self.access_token:
            url += f"?access_token={self.access_token}"
        return url
    
    async def connect(self) -> bool:
        """连接到 NapCat"""
        try:
            self.logger.info(f"正在连接到 NapCat: {self.websocket_url}")
            
            # 设置连接头
            headers = {}
            if self.access_token:
                headers["Authorization"] = f"Bearer {self.access_token}"
            
            # 建立 WebSocket 连接
            # 兼容不同版本的 websockets 库
            connect_kwargs = {
                "ping_interval": 20,
                "ping_timeout": 10
            }
            
            # 获取实际的连接 URL
            connection_url = self.websocket_url
            
            # 只有在有认证头且支持时才添加 extra_headers
            if headers:
                try:
                    # 检查 websockets.connect 是否支持 extra_headers 参数
                    import inspect
                    sig = inspect.signature(websockets.connect)
                    if 'extra_headers' in sig.parameters:
                        connect_kwargs["extra_headers"] = headers
                        self.logger.debug("使用 extra_headers 进行认证")
                    else:
                        # 如果不支持 extra_headers，尝试其他方式
                        self.logger.warning("当前 websockets 版本不支持 extra_headers，将使用 URL 参数认证")
                        if self.access_token:
                            # 将认证信息添加到 URL 中（如果 NapCat 支持）
                            separator = "&" if "?" in connection_url else "?"
                            connection_url = f"{connection_url}{separator}access_token={self.access_token}"
                            self.logger.debug(f"修改后的连接 URL: {connection_url}")
                except Exception as e:
                    self.logger.warning(f"检查 websockets 参数支持时出错: {e}")
            
            # 尝试多种连接方式
            connection_attempts = [
                # 尝试 1: 使用完整参数
                lambda: websockets.connect(connection_url, **connect_kwargs),
                # 尝试 2: 只使用基本参数
                lambda: websockets.connect(connection_url, ping_interval=20, ping_timeout=10),
                # 尝试 3: 最简化连接
                lambda: websockets.connect(connection_url)
            ]
            
            last_error = None
            for i, attempt in enumerate(connection_attempts, 1):
                try:
                    self.logger.debug(f"尝试连接方式 {i}")
                    self.websocket = await attempt()
                    break
                except TypeError as e:
                    if "unexpected keyword argument" in str(e):
                        self.logger.warning(f"连接方式 {i} 参数不兼容: {e}")
                        last_error = e
                        continue
                    else:
                        raise
                except Exception as e:
                    self.logger.warning(f"连接方式 {i} 失败: {e}")
                    last_error = e
                    if i < len(connection_attempts):
                        continue
                    else:
                        raise
            
            if not self.websocket:
                raise Exception(f"所有连接方式都失败，最后一个错误: {last_error}")
            
            self.connected = True
            self.reconnect_attempts = 0
            self.last_heartbeat = time.time()
            
            self.logger.info("成功连接到 NapCat")
            return True
            
        except Exception as e:
            self.logger.error(f"连接 NapCat 失败: {e}")
            self.logger.error(f"连接详情 - URL: {self.websocket_url}, Token: {'***' if self.access_token else 'None'}")
            
            # 提供详细的错误诊断
            if "unexpected keyword argument 'extra_headers'" in str(e):
                self.logger.error("错误原因: websockets 库版本不支持 extra_headers 参数")
                self.logger.error("解决方案: 运行 python fix_napcat_connection.py 进行修复")
            elif "Connection refused" in str(e):
                self.logger.error("错误原因: NapCat 服务未启动或连接信息错误")
                self.logger.error("解决方案: 1) 检查 NapCat 是否运行 2) 确认端口和地址配置")
            elif "timeout" in str(e).lower():
                self.logger.error("错误原因: 连接超时")
                self.logger.error("解决方案: 检查网络连接和防火墙设置")
            
            self.connected = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            self.running = False
            self.connected = False
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            self.logger.info("已断开与 NapCat 的连接")
            
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    async def start(self):
        """启动客户端"""
        self.running = True
        
        while self.running:
            try:
                if not self.connected:
                    if await self.connect():
                        # 连接成功，开始监听消息
                        await self._listen_messages()
                    else:
                        # 连接失败，等待重连
                        await self._handle_reconnect()
                else:
                    # 已连接，继续监听
                    await self._listen_messages()
                    
            except Exception as e:
                self.logger.error(f"客户端运行错误: {e}")
                await self._handle_reconnect()
    
    async def _listen_messages(self):
        """监听消息"""
        try:
            if not self.websocket:
                return

            async for message in self.websocket:
                try:
                    # 更新心跳时间
                    self.last_heartbeat = time.time()

                    # 解析原始消息
                    try:
                        data = json.loads(message)
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSON解析失败: {e}")
                        continue

                    # 区分消息类型
                    if "post_type" in data:
                        # 这是一个事件
                        event = self.parser.parse_event(message)
                        if event:
                            await self._dispatch_event(event)
                    elif "echo" in data:
                        # 这是一个API响应
                        await self._handle_api_response(data)
                    else:
                        # 未知消息类型
                        self.logger.debug(f"收到未知类型消息: {data}")

                except Exception as e:
                    self.logger.error(f"处理消息时发生错误: {e}")

        except ConnectionClosed:
            self.logger.warning("WebSocket 连接已关闭")
            self.connected = False
        except WebSocketException as e:
            self.logger.error(f"WebSocket 错误: {e}")
            self.connected = False
        except Exception as e:
            self.logger.error(f"监听消息时发生错误: {e}")
            self.connected = False

    async def _handle_api_response(self, response_data: Dict[str, Any]):
        """处理API响应"""
        try:
            echo = response_data.get("echo")
            if not echo:
                self.logger.debug("收到没有echo的API响应")
                return

            # 处理同步API调用（等待响应的）
            if echo in self.pending_api_calls:
                future = self.pending_api_calls.pop(echo)
                if not future.done():
                    if response_data.get("status") == "ok":
                        future.set_result(response_data.get("data"))
                        self.logger.debug(f"同步API调用成功: {echo[:8]}...")
                    else:
                        error_msg = response_data.get("message", "未知错误")
                        future.set_exception(Exception(f"API调用失败: {error_msg}"))
                        self.logger.error(f"同步API调用失败: {echo[:8]}... - {error_msg}")
                return

            # 处理异步API调用（回调模式）
            if echo in self.response_callbacks:
                callback = self.response_callbacks.pop(echo)
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(response_data)
                    else:
                        callback(response_data)
                    self.logger.debug(f"异步API响应回调执行成功: {echo[:8]}...")
                except Exception as e:
                    self.logger.error(f"异步API响应回调执行失败: {echo[:8]}... - {e}")
                return

            # 未匹配的响应（可能是快速发送模式的响应）
            self.logger.debug(f"收到未匹配的API响应: {echo[:8]}... (可能是快速发送模式)")

        except Exception as e:
            self.logger.error(f"处理API响应失败: {e}")

    async def _dispatch_event(self, event: OneBotEvent):
        """分发事件到处理器"""
        try:
            event_type = event.post_type
            
            # 记录事件
            self.logger.debug(f"收到事件: {self.parser.get_event_summary(event)}")
            
            # 调用通用事件处理器
            await self._call_handlers("*", event)
            
            # 调用特定类型的事件处理器
            await self._call_handlers(event_type, event)
            
            # 调用更具体的处理器（如果是消息事件）
            if hasattr(event, 'message_type'):
                await self._call_handlers(f"{event_type}.{event.message_type}", event)
            elif hasattr(event, 'notice_type'):
                await self._call_handlers(f"{event_type}.{event.notice_type}", event)
            elif hasattr(event, 'request_type'):
                await self._call_handlers(f"{event_type}.{event.request_type}", event)
            elif hasattr(event, 'meta_event_type'):
                await self._call_handlers(f"{event_type}.{event.meta_event_type}", event)
                
        except Exception as e:
            self.logger.error(f"分发事件时发生错误: {e}")
    
    async def _call_handlers(self, event_type: str, event: OneBotEvent):
        """调用事件处理器"""
        handlers = self.event_handlers.get(event_type, [])
        
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    handler(event)
            except Exception as e:
                self.logger.error(f"事件处理器执行失败 ({event_type}): {e}")
    
    async def _handle_reconnect(self):
        """处理重连"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error(f"重连次数已达上限 ({self.max_reconnect_attempts})，停止重连")
            self.running = False
            return
        
        self.reconnect_attempts += 1
        wait_time = min(self.reconnect_interval * self.reconnect_attempts, 60)
        
        self.logger.info(f"第 {self.reconnect_attempts} 次重连，等待 {wait_time} 秒...")
        await asyncio.sleep(wait_time)
    
    def on(self, event_type: str, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        
        self.event_handlers[event_type].append(handler)
        self.logger.info(f"注册事件处理器: {event_type}")
    
    def off(self, event_type: str, handler: Callable):
        """移除事件处理器"""
        if event_type in self.event_handlers:
            try:
                self.event_handlers[event_type].remove(handler)
                self.logger.info(f"移除事件处理器: {event_type}")
            except ValueError:
                self.logger.warning(f"事件处理器不存在: {event_type}")
    
    async def send_message(self, message_type: str, target_id: int, message: str,
                          wait_response: bool = False, callback: Optional[Callable] = None) -> bool:
        """发送消息（快速发送模式，默认不等待响应）"""
        try:
            # 验证参数
            if not message_type or not message:
                self.logger.error("消息类型或内容不能为空")
                return False

            if message_type not in ["private", "group"]:
                self.logger.error(f"不支持的消息类型: {message_type}")
                return False

            # 构造OneBot API调用消息
            if message_type == "private":
                action = "send_private_msg"
                params = {"user_id": target_id, "message": message}
                log_target = f"用户{target_id}"
            elif message_type == "group":
                action = "send_group_msg"
                params = {"group_id": target_id, "message": message}
                log_target = f"群{target_id}"
            else:
                return False

            self.logger.info(f"发送消息到{log_target}: {message[:50]}{'...' if len(message) > 50 else ''}")

            if wait_response:
                # 需要等待响应的模式（用于获取message_id等场景）
                return await self._send_message_with_response(action, params, log_target)
            else:
                # 快速发送模式（默认）- 发送后立即返回，支持异步回调
                return await self._send_message_fast(action, params, log_target, callback)

        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return False

    async def _send_message_fast(self, action: str, params: Dict[str, Any], log_target: str,
                                callback: Optional[Callable] = None) -> bool:
        """快速发送消息，符合OneBot规范，支持异步回调"""
        try:
            # 检查连接状态
            if not await self._ensure_connection():
                self.logger.error(f"WebSocket连接不可用，无法发送消息到{log_target}")
                return False

            # 生成echo字段（符合OneBot规范）
            import uuid
            echo = str(uuid.uuid4())

            # 如果提供了回调函数，注册它
            if callback:
                self.response_callbacks[echo] = callback

            # 构造请求消息（符合OneBot规范，包含echo字段）
            request = {
                "action": action,
                "params": params,
                "echo": echo
            }

            # 发送请求
            await self.websocket.send(json.dumps(request))
            self.logger.info(f"消息已发送到{log_target} (echo: {echo[:8]}...)")
            return True

        except Exception as e:
            self.logger.error(f"快速发送消息到{log_target}失败: {e}")
            return False

    async def _send_message_with_response(self, action: str, params: Dict[str, Any], log_target: str) -> bool:
        """发送消息并等待响应（用于需要message_id的场景）"""
        try:
            # 调用WebSocket API并等待响应
            result = await self._call_websocket_api(action, params)
            if result is not None:
                message_id = result.get('message_id') if isinstance(result, dict) else None
                self.logger.info(f"消息发送成功到{log_target}" + (f" (消息ID: {message_id})" if message_id else ""))
                return True
            else:
                self.logger.error(f"消息发送失败到{log_target}: API返回空结果")
                return False

        except Exception as e:
            error_msg = str(e)
            if "API响应超时" in error_msg:
                # 对于超时错误，消息很可能已经发送成功
                self.logger.warning(f"发送消息到{log_target}响应超时 - 消息可能已成功发送")
                return True
            else:
                self.logger.error(f"发送消息到{log_target}失败: {e}")
                return False

    async def _call_websocket_api(self, action: str, params: Dict[str, Any], timeout: Optional[float] = None) -> Any:
        """通过WebSocket调用API并等待响应（无重试）"""
        if timeout is None:
            timeout = self.api_timeout

        try:
            # 检查连接状态
            if not await self._ensure_connection():
                raise Exception("WebSocket连接不可用")

            # 使用锁防止并发API调用冲突
            async with self._send_lock:
                return await self._single_api_call(action, params, timeout)

        except asyncio.TimeoutError:
            # 对于超时，给出更友好的提示
            self.logger.warning(f"API调用响应超时 ({timeout}s): {action} - 消息可能已发送成功，但服务器响应超时")
            raise Exception(f"API响应超时: {action}")

        except Exception as e:
            self.logger.error(f"API调用失败: {action} - {e}")
            raise e

    async def _single_api_call(self, action: str, params: Dict[str, Any], timeout: float) -> Any:
        """执行单次API调用"""
        echo = None
        try:
            # 生成唯一的echo标识
            import uuid
            echo = str(uuid.uuid4())

            # 创建Future对象等待响应
            future = asyncio.Future()
            self.pending_api_calls[echo] = future

            # 构造请求消息
            request = {
                "action": action,
                "params": params,
                "echo": echo
            }

            # 发送请求
            await self.websocket.send(json.dumps(request))
            self.logger.debug(f"发送WebSocket API请求: {action} (echo: {echo[:8]}...)")

            # 等待响应
            try:
                result = await asyncio.wait_for(future, timeout=timeout)
                self.logger.debug(f"API调用成功: {action} (echo: {echo[:8]}...)")
                return result
            except asyncio.TimeoutError:
                # 清理超时的请求
                self.pending_api_calls.pop(echo, None)
                self.logger.error(f"API调用超时 ({timeout}s): {action}")
                raise

        except Exception as e:
            # 清理失败的请求
            if echo:
                self.pending_api_calls.pop(echo, None)
            raise e

    async def _ensure_connection(self) -> bool:
        """确保WebSocket连接可用"""
        try:
            if not self.websocket or not self.connected:
                return False

            # 检查WebSocket状态（websockets 15.0+使用state属性）
            try:
                from websockets.protocol import State
                if hasattr(self.websocket, 'state') and self.websocket.state != State.OPEN:
                    self.connected = False
                    return False
            except ImportError:
                # 如果无法导入State，跳过状态检查
                pass

            # 简单的连接测试（发送ping）
            try:
                pong_waiter = await self.websocket.ping()
                await asyncio.wait_for(pong_waiter, timeout=5.0)
                return True
            except Exception:
                self.connected = False
                return False

        except Exception as e:
            self.logger.error(f"连接检查失败: {e}")
            self.connected = False
            return False
    
    async def get_login_info(self) -> Optional[Dict[str, Any]]:
        """获取登录信息（通过WebSocket）"""
        try:
            return await self._call_websocket_api("get_login_info", {})
        except Exception as e:
            self.logger.error(f"获取登录信息失败: {e}")
            return None

    async def get_friend_list(self) -> Optional[List[Dict[str, Any]]]:
        """获取好友列表（通过WebSocket）"""
        try:
            return await self._call_websocket_api("get_friend_list", {})
        except Exception as e:
            self.logger.error(f"获取好友列表失败: {e}")
            return None

    async def get_group_list(self) -> Optional[List[Dict[str, Any]]]:
        """获取群列表（通过WebSocket）"""
        try:
            return await self._call_websocket_api("get_group_list", {})
        except Exception as e:
            self.logger.error(f"获取群列表失败: {e}")
            return None

    # ==================== OneBot API 方法（通过WebSocket） ====================

    async def delete_msg(self, message_id: int) -> Optional[Dict[str, Any]]:
        """撤回消息"""
        try:
            return await self._call_websocket_api("delete_msg", {"message_id": message_id})
        except Exception as e:
            self.logger.error(f"撤回消息失败: {e}")
            return None

    async def get_msg(self, message_id: int) -> Optional[Dict[str, Any]]:
        """获取消息"""
        try:
            return await self._call_websocket_api("get_msg", {"message_id": message_id})
        except Exception as e:
            self.logger.error(f"获取消息失败: {e}")
            return None

    async def get_forward_msg(self, id: str) -> Optional[Dict[str, Any]]:
        """获取合并转发消息"""
        try:
            return await self._call_websocket_api("get_forward_msg", {"id": id})
        except Exception as e:
            self.logger.error(f"获取合并转发消息失败: {e}")
            return None

    async def send_like(self, user_id: int, times: int = 1) -> Optional[Dict[str, Any]]:
        """发送好友赞"""
        try:
            return await self._call_websocket_api("send_like", {"user_id": user_id, "times": times})
        except Exception as e:
            self.logger.error(f"发送好友赞失败: {e}")
            return None

    async def set_group_kick(self, group_id: int, user_id: int, reject_add_request: bool = False) -> Optional[Dict[str, Any]]:
        """群组踢人"""
        try:
            return await self._call_websocket_api("set_group_kick", {
                "group_id": group_id,
                "user_id": user_id,
                "reject_add_request": reject_add_request
            })
        except Exception as e:
            self.logger.error(f"群组踢人失败: {e}")
            return None

    async def set_group_ban(self, group_id: int, user_id: int, duration: int = 30 * 60) -> Optional[Dict[str, Any]]:
        """群组单人禁言"""
        try:
            return await self._call_websocket_api("set_group_ban", {
                "group_id": group_id,
                "user_id": user_id,
                "duration": duration
            })
        except Exception as e:
            self.logger.error(f"群组单人禁言失败: {e}")
            return None

    async def set_group_whole_ban(self, group_id: int, enable: bool = True) -> Optional[Dict[str, Any]]:
        """群组全员禁言"""
        try:
            return await self._call_websocket_api("set_group_whole_ban", {
                "group_id": group_id,
                "enable": enable
            })
        except Exception as e:
            self.logger.error(f"群组全员禁言失败: {e}")
            return None

    async def set_group_admin(self, group_id: int, user_id: int, enable: bool = True) -> Optional[Dict[str, Any]]:
        """群组设置管理员"""
        try:
            return await self._call_websocket_api("set_group_admin", {
                "group_id": group_id,
                "user_id": user_id,
                "enable": enable
            })
        except Exception as e:
            self.logger.error(f"群组设置管理员失败: {e}")
            return None

    async def set_group_card(self, group_id: int, user_id: int, card: str = "") -> Optional[Dict[str, Any]]:
        """设置群名片（群备注）"""
        try:
            return await self._call_websocket_api("set_group_card", {
                "group_id": group_id,
                "user_id": user_id,
                "card": card
            })
        except Exception as e:
            self.logger.error(f"设置群名片失败: {e}")
            return None

    async def set_group_name(self, group_id: int, group_name: str) -> Optional[Dict[str, Any]]:
        """设置群名"""
        try:
            return await self._call_websocket_api("set_group_name", {
                "group_id": group_id,
                "group_name": group_name
            })
        except Exception as e:
            self.logger.error(f"设置群名失败: {e}")
            return None

    async def set_group_leave(self, group_id: int, is_dismiss: bool = False) -> Optional[Dict[str, Any]]:
        """退出群组"""
        try:
            return await self._call_websocket_api("set_group_leave", {
                "group_id": group_id,
                "is_dismiss": is_dismiss
            })
        except Exception as e:
            self.logger.error(f"退出群组失败: {e}")
            return None

    async def get_stranger_info(self, user_id: int, no_cache: bool = False) -> Optional[Dict[str, Any]]:
        """获取陌生人信息"""
        try:
            return await self._call_websocket_api("get_stranger_info", {
                "user_id": user_id,
                "no_cache": no_cache
            })
        except Exception as e:
            self.logger.error(f"获取陌生人信息失败: {e}")
            return None

    async def get_group_info(self, group_id: int, no_cache: bool = False) -> Optional[Dict[str, Any]]:
        """获取群信息"""
        try:
            return await self._call_websocket_api("get_group_info", {
                "group_id": group_id,
                "no_cache": no_cache
            })
        except Exception as e:
            self.logger.error(f"获取群信息失败: {e}")
            return None

    async def get_group_member_list(self, group_id: int) -> Optional[List[Dict[str, Any]]]:
        """获取群成员列表"""
        try:
            return await self._call_websocket_api("get_group_member_list", {"group_id": group_id})
        except Exception as e:
            self.logger.error(f"获取群成员列表失败: {e}")
            return None

    async def get_group_member_info(self, group_id: int, user_id: int, no_cache: bool = False) -> Optional[Dict[str, Any]]:
        """获取群成员信息"""
        try:
            return await self._call_websocket_api("get_group_member_info", {
                "group_id": group_id,
                "user_id": user_id,
                "no_cache": no_cache
            })
        except Exception as e:
            self.logger.error(f"获取群成员信息失败: {e}")
            return None

    async def set_friend_add_request(self, flag: str, approve: bool = True, remark: str = "") -> Optional[Dict[str, Any]]:
        """处理加好友请求"""
        try:
            return await self._call_websocket_api("set_friend_add_request", {
                "flag": flag,
                "approve": approve,
                "remark": remark
            })
        except Exception as e:
            self.logger.error(f"处理加好友请求失败: {e}")
            return None

    async def set_group_add_request(self, flag: str, sub_type: str, approve: bool = True, reason: str = "") -> Optional[Dict[str, Any]]:
        """处理加群请求／邀请"""
        try:
            return await self._call_websocket_api("set_group_add_request", {
                "flag": flag,
                "sub_type": sub_type,
                "approve": approve,
                "reason": reason
            })
        except Exception as e:
            self.logger.error(f"处理加群请求失败: {e}")
            return None
    
    def is_connected(self) -> bool:
        """检查连接状态（基础检查）"""
        if not self.connected or not self.websocket:
            return False

        # 检查WebSocket状态（websockets 15.0+使用state属性）
        try:
            from websockets.protocol import State
            if hasattr(self.websocket, 'state'):
                return self.websocket.state == State.OPEN
        except ImportError:
            # 如果无法导入State，只检查基础状态
            pass

        return self.connected

    async def is_connection_healthy(self) -> bool:
        """检查连接健康状态（包含ping测试）"""
        try:
            if not self.is_connected():
                return False

            # 发送ping测试连接
            pong_waiter = await self.websocket.ping()
            await asyncio.wait_for(pong_waiter, timeout=3.0)
            return True

        except Exception as e:
            self.logger.debug(f"连接健康检查失败: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取客户端状态"""
        websocket_state = "closed"
        if self.websocket:
            try:
                from websockets.protocol import State
                if hasattr(self.websocket, 'state'):
                    websocket_state = self.websocket.state.name.lower()
                else:
                    websocket_state = "unknown"
            except ImportError:
                websocket_state = "connected" if self.connected else "closed"

        return {
            "connected": self.connected,
            "running": self.running,
            "reconnect_attempts": self.reconnect_attempts,
            "last_heartbeat": self.last_heartbeat,
            "websocket_url": self.websocket_url,
            "websocket_state": websocket_state,
            "pending_api_calls": len(self.pending_api_calls),
            "response_callbacks": len(self.response_callbacks),
            "api_timeout": self.api_timeout,
            "event_handlers": {k: len(v) for k, v in self.event_handlers.items()}
        }
