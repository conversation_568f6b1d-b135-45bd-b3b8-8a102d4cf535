# QQBot 云端转发器

基于 NapCat (OneBot v11) 的 QQ 机器人，专门用于消息转发到云端 API。

## 🎯 核心特性

- **纯消息转发**: QQBot 不再处理任何业务逻辑，只负责消息收发
- **云端优先**: 所有消息都转发到 Admin API 处理
- **轻量级**: 移除了本地插件系统和数据库依赖
- **高可用**: 支持自动重试和错误处理

## 📁 项目结构

```
QQbot/
├── main_napcat.py              # 主程序入口
├── adapters/                   # 适配器
│   ├── napcat/                # NapCat OneBot适配器
│   └── cloud_api_client.py    # 云端API客户端
├── config/                    # 配置文件
│   ├── cloud_config.yml       # 云端API配置
│   └── cloud_config.py        # 配置管理
└── utils/                     # 工具模块
    ├── logger.py              # 日志系统
    └── helpers.py             # 辅助函数
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置云端API

编辑 `config/cloud_config.yml`:

```yaml
cloud_api:
  enabled: true
  base_url: "https://your-domain.com/qqbot/BusinessDoc"
  auth_token: "your-secure-token-here"
  timeout: 30
  max_retries: 3

command_detection:
  prefixes:
    - "/"
    - "！"
    - "!"
```

### 3. 启动机器人

```bash
python main_napcat.py
```

## ⚙️ 配置说明

### 云端API配置

- `enabled`: 是否启用云端API
- `base_url`: Admin API的基础URL
- `auth_token`: 认证Token
- `timeout`: 请求超时时间（秒）
- `max_retries`: 最大重试次数

### 命令检测配置

- `prefixes`: 命令前缀列表，只有以这些前缀开头的消息才会转发

### 工作模式

- **命令转发模式**: QQBot只转发以 `/`、`！`、`!` 开头的命令消息到云端API
- **智能过滤**: 自动检测命令前缀，非命令消息不转发
- **云端决策**: 所有业务逻辑由PHP Admin API处理

## 📝 日志说明

QQBot 会记录以下关键日志：
- 云端API初始化状态
- 消息转发和响应情况
- 连接错误和重试情况
- API调用性能统计

查看日志文件：`logs/qqbot.log`

## 🔧 故障排除

### 1. 云端API连接失败
- 检查网络连接
- 确认API地址和端口
- 验证认证Token是否正确

### 2. 消息没有回复
- 查看Admin API日志
- 确认API接口正常工作
- 检查PHP端的消息处理逻辑

### 3. 性能问题
- 调整timeout和max_retries参数
- 检查网络延迟
- 优化API服务器性能

## 📄 许可证

本项目遵循 MIT 许可证。
